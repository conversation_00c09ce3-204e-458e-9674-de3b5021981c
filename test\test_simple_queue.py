#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 最简单的队列测试
完全按照官方demo的方式
"""

import sys
import os
import time
import math

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001

# 全局变量（按照官方demo）
Size = 0
pos = nrc.VectorDouble()

def connect(ip, port):
    fd = nrc.connect_robot(ip, port)
    print("初始化控制器ID: ", fd)
    return fd

def clear_error(socketFd):
    nrc.clear_error(socketFd)
    print('清错')

def power_on(socketFd):
    nrc.set_servo_poweron(socketFd)
    print('上使能')

def get_pos():
    """按照官方demo创建一个简单的位置命令"""
    pos = nrc.VectorDouble()
    # 使用一个简单的关节角度 [0, 0, 0, 0, 0, 0]
    axis = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

    # 创建并填充 VectorDouble
    for value in axis:
        pos.append(value)

    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = pos
    print('cmd:', list(cmd.targetPosValue))
    
    return cmd

def queue_cmd(socketFd, name):
    """按照官方demo的队列命令函数"""
    global Size
    global pos
    
    if name == 'start':
        result = nrc.queue_motion_set_status(socketFd, True)
        print(f"启动队列模式结果: {result}")
    elif name == 'movj':
        cmd = get_pos()
        cmd.velocity = 80
        cmd.acc = 100
        cmd.dec = 100
        print('cmd: ', list(cmd.targetPosValue))
        result = nrc.queue_motion_push_back_moveJ(socketFd, cmd)
        print(f"添加moveJ指令结果: {result}")
        if result == 0:
            Size += 1
            print(f"成功添加指令，当前Size: {Size}")
        else:
            print('指令插入失败')
    elif name == 'send':
        result = nrc.queue_motion_send_to_controller(socketFd, Size)
        print('运动队列长度: ', Size)
        print(f"发送队列结果: {result}")
        time.sleep(0.5)
        
        # 检查队列长度
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socketFd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
        print(f"发送后队列长度: {queue_len}")
        
        Size = 0
        jug = 1
        while jug != 0:
            # status的类型是一个int类型，获取到状态之后status会变成一个列表，所以再次调用接口的时候需要重新定义status为int类型传入
            status = 1
            status = nrc.get_robot_running_state(socketFd, status) 
            jug = status[1]
            print(f"运行状态: {jug}")
            time.sleep(0.5)

def test_simple_queue():
    """最简单的队列测试"""
    print("=" * 60)
    print("INEXBOT机械臂 最简单队列测试")
    print("完全按照官方demo方式")
    print("=" * 60)
    
    # 1. 连接
    socketFd = connect(ROBOT_IP, str(ROBOT_PORT))
    if socketFd <= 0:
        print(f"❌ 连接失败，Socket ID: {socketFd}")
        return
    
    try:
        # 2. 清错和上电
        clear_error(socketFd)
        time.sleep(0.5)
        power_on(socketFd)
        time.sleep(2)
        
        # 3. 设置远程模式
        print("设置远程模式...")
        result = nrc.set_current_mode(socketFd, 1)  # 1 = 远程模式
        print(f"设置远程模式结果: {result}")
        time.sleep(0.5)
        
        # 4. 启动队列模式
        print("\n=== 启动队列模式 ===")
        queue_cmd(socketFd, 'start')
        time.sleep(1)
        
        # 5. 添加3个简单的moveJ指令
        print("\n=== 添加指令到队列 ===")
        for i in range(3):
            print(f"\n添加第 {i+1} 个指令:")
            queue_cmd(socketFd, 'movj')
            time.sleep(0.1)
        
        # 6. 发送队列并执行
        print("\n=== 发送队列并执行 ===")
        queue_cmd(socketFd, 'send')
        
        print("\n✅ 队列测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理
        try:
            print("\n=== 清理队列模式 ===")
            nrc.queue_motion_set_status(socketFd, False)
            nrc.set_current_mode(socketFd, 0)  # 0 = 示教模式
            print("队列模式已关闭")
        except Exception as e:
            print(f"清理时发生错误: {e}")
        
        # 下电和断开
        print("\n=== 下电和断开 ===")
        nrc.set_servo_poweroff(socketFd)
        time.sleep(1)
        nrc.disconnect_robot(socketFd)
        print("已断开连接")

if __name__ == "__main__":
    test_simple_queue()
