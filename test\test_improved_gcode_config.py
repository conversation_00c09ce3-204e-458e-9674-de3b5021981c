#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的G代码处理功能测试配置

修改improved_gcode_processor.py中的GCODE_FILE变量来使用测试文件
"""

import sys
import os

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

# 修改improved_gcode_processor.py中的配置
def update_test_config():
    """更新测试配置"""
    processor_file = os.path.join(os.path.dirname(__file__), 'improved_gcode_processor.py')
    
    # 读取文件内容
    with open(processor_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换测试文件名
    content = content.replace('GCODE_FILE = "jiyi.Gcode"', 'GCODE_FILE = "test_improved_gcode.gcode"')
    
    # 写回文件
    with open(processor_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 测试配置已更新")

if __name__ == "__main__":
    update_test_config()
    
    # 导入并运行改进的G代码处理器
    from improved_gcode_processor import execute_improved_gcode
    
    print("🚀 开始测试改进的G代码处理功能...")
    success = execute_improved_gcode()
    
    if success:
        print("✅ 测试完成！")
    else:
        print("❌ 测试失败！")
