#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角度平滑算法测试脚本
测试ABC轴角度平滑处理功能，无需连接实际机械臂
"""

import sys
import os
import math

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(__file__))

from improved_gcode_processor import AngleSmoothing, GCodeInstruction

def test_angle_normalization():
    """测试角度标准化功能"""
    print("=== 测试角度标准化功能 ===")
    
    test_cases = [
        (0, 0),
        (180, 180),
        (181, -179),
        (-181, 179),
        (360, 0),
        (-360, 0),
        (450, 90),
        (-270, 90)
    ]
    
    for input_angle, expected in test_cases:
        result = AngleSmoothing.normalize_angle_degrees(input_angle)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"  {status} {input_angle}° → {result}° (期望: {expected}°)")
    
    print()

def test_angle_difference():
    """测试角度差值计算"""
    print("=== 测试角度差值计算 ===")
    
    test_cases = [
        ((0, 90), 90),
        ((0, -90), -90),
        ((170, -170), 20),  # 最短路径
        ((-170, 170), -20), # 最短路径
        ((0, 180), 180),
        ((0, -180), 180)
    ]
    
    for (angle1, angle2), expected in test_cases:
        result = AngleSmoothing.calculate_angle_difference(angle1, angle2)
        status = "✅" if abs(result - expected) < 0.001 else "❌"
        print(f"  {status} {angle1}° → {angle2}° = {result}° (期望: {expected}°)")
    
    print()

def test_angle_jump_detection():
    """测试角度突变检测"""
    print("=== 测试角度突变检测 ===")
    
    test_cases = [
        # (prev_angles, curr_angles, threshold, expected)
        ((0, 0, 0), (10, 10, 10), 30, False),  # 小变化
        ((0, 0, 0), (40, 0, 0), 30, True),     # A轴大变化
        ((0, 0, 0), (0, 40, 0), 30, True),     # B轴大变化
        ((0, 0, 0), (0, 0, 40), 30, True),     # C轴大变化
        ((170, 0, 0), (-170, 0, 0), 30, True), # 跨越边界的大变化
        ((170, 0, 0), (-160, 0, 0), 30, False) # 跨越边界的小变化
    ]
    
    for prev_angles, curr_angles, threshold, expected in test_cases:
        result = AngleSmoothing.detect_angle_jump(prev_angles, curr_angles, threshold)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {prev_angles} → {curr_angles} (阈值:{threshold}°) = {result} (期望: {expected})")
    
    print()

def test_smooth_transition():
    """测试角度平滑过渡生成"""
    print("=== 测试角度平滑过渡生成 ===")
    
    # 测试用例1: 简单线性过渡
    start_angles = (0, 0, 0)
    end_angles = (60, 30, 0)
    steps = 3
    
    print(f"测试用例1: {start_angles} → {end_angles} (步数: {steps})")
    transitions = AngleSmoothing.generate_smooth_transition(start_angles, end_angles, steps)
    
    for i, angles in enumerate(transitions):
        print(f"  步骤 {i+1}: A={angles[0]:.1f}°, B={angles[1]:.1f}°, C={angles[2]:.1f}°")
    
    # 验证最后一步是否为目标角度
    final_angles = transitions[-1]
    if all(abs(final_angles[i] - end_angles[i]) < 0.001 for i in range(3)):
        print("  ✅ 最终角度正确")
    else:
        print("  ❌ 最终角度错误")
    
    print()
    
    # 测试用例2: 跨越边界的过渡
    start_angles = (170, 0, 0)
    end_angles = (-170, 0, 0)
    steps = 5
    
    print(f"测试用例2: {start_angles} → {end_angles} (步数: {steps})")
    transitions = AngleSmoothing.generate_smooth_transition(start_angles, end_angles, steps)
    
    for i, angles in enumerate(transitions):
        print(f"  步骤 {i+1}: A={angles[0]:.1f}°, B={angles[1]:.1f}°, C={angles[2]:.1f}°")
    
    print()

def test_gcode_instruction():
    """测试G代码指令数据结构"""
    print("=== 测试G代码指令数据结构 ===")
    
    # 创建G0指令
    g0_instruction = GCodeInstruction("G0", 10.0, 20.0, 30.0)
    print(f"G0指令: {g0_instruction}")
    
    # 创建G1指令（带ABC角度）
    g1_instruction = GCodeInstruction("G1", 15.0, 25.0, 35.0, 45.0, 30.0, 15.0)
    print(f"G1指令: {g1_instruction}")
    
    print()

def test_practical_scenario():
    """测试实际应用场景"""
    print("=== 测试实际应用场景 ===")
    
    # 模拟G0到G1的角度过渡
    print("场景1: G0到G1的角度过渡")
    g0_angles = (180, 0, 0)  # G0固定角度
    g1_angles = (0, 0, 0)    # G1目标角度
    
    print(f"G0角度: A={g0_angles[0]}°, B={g0_angles[1]}°, C={g0_angles[2]}°")
    print(f"G1角度: A={g1_angles[0]}°, B={g1_angles[1]}°, C={g1_angles[2]}°")
    
    if AngleSmoothing.detect_angle_jump(g0_angles, g1_angles, 30):
        print("检测到角度突变，生成平滑过渡:")
        transitions = AngleSmoothing.generate_smooth_transition(g0_angles, g1_angles, 5)
        for i, angles in enumerate(transitions):
            print(f"  过渡步骤 {i+1}: A={angles[0]:.1f}°, B={angles[1]:.1f}°, C={angles[2]:.1f}°")
    else:
        print("未检测到角度突变")
    
    print()
    
    # 模拟G1序列中的角度变化
    print("场景2: G1序列中的角度变化")
    g1_sequence = [
        (0, 0, 0),
        (10, 5, 0),
        (45, 30, 15),  # 大角度变化
        (50, 35, 20)
    ]
    
    for i in range(1, len(g1_sequence)):
        prev_angles = g1_sequence[i-1]
        curr_angles = g1_sequence[i]
        
        print(f"G1步骤 {i}: {prev_angles} → {curr_angles}")
        
        if AngleSmoothing.detect_angle_jump(prev_angles, curr_angles, 30):
            print("  检测到角度突变，需要平滑处理")
            transitions = AngleSmoothing.generate_smooth_transition(prev_angles, curr_angles, 3)
            for j, angles in enumerate(transitions[:-1]):  # 最后一个由原指令处理
                print(f"    插入过渡: A={angles[0]:.1f}°, B={angles[1]:.1f}°, C={angles[2]:.1f}°")
        else:
            print("  角度变化正常，无需平滑处理")
    
    print()

def main():
    """主测试函数"""
    print("🧪 ABC轴角度平滑算法测试")
    print("=" * 50)
    
    test_angle_normalization()
    test_angle_difference()
    test_angle_jump_detection()
    test_smooth_transition()
    test_gcode_instruction()
    test_practical_scenario()
    
    print("✅ 所有测试完成！")

if __name__ == "__main__":
    main()
