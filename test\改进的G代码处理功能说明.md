# INEXBOT机械臂 改进的G代码处理功能说明

## 功能概述

本改进方案基于现有的用户坐标系统(UCS1)和队列模式实现，提供了以下三个主要功能改进：

### 1. G0指令独立队列处理
- **执行速度**: 100%（最大速度）
- **ABC轴角度**: 固定值 A=180°, B=0°, C=0°
- **特点**: 不检测ABC轴角度变化，专注于快速定位

### 2. G1指令速度控制
- **执行速度**: 30%
- **ABC轴角度**: 支持G代码中指定的角度值
- **特点**: 精确控制，适合精密加工

### 3. ABC轴角度平滑处理
- **角度突变检测**: 当角度变化超过30°时触发平滑处理
- **平滑算法**: 使用插值方法生成5个过渡步骤
- **应用场景**: 
  - G0最后一个点到G1第一个点之间的角度突变
  - G1指令序列内部的大幅角度变化

## 文件结构

```
test/
├── improved_gcode_processor.py      # 主要实现文件
├── test_improved_gcode.gcode        # 测试G代码文件
├── test_improved_gcode_config.py    # 测试配置脚本
└── 改进的G代码处理功能说明.md      # 本说明文档
```

## 核心类和函数

### 1. GCodeInstruction 类
```python
class GCodeInstruction:
    """G代码指令数据结构"""
    def __init__(self, instruction_type: str, x: float, y: float, z: float, 
                 a: Optional[float] = None, b: Optional[float] = None, c: Optional[float] = None)
```

### 2. AngleSmoothing 类
```python
class AngleSmoothing:
    """ABC轴角度平滑处理类"""
    
    @staticmethod
    def detect_angle_jump(prev_angles, curr_angles, threshold=30.0) -> bool:
        """检测是否存在角度突变"""
    
    @staticmethod
    def generate_smooth_transition(start_angles, end_angles, steps=5) -> List:
        """生成角度平滑过渡序列"""
```

### 3. ImprovedGCodeProcessor 类
```python
class ImprovedGCodeProcessor:
    """改进的G代码处理器"""
    
    def parse_gcode_file(self, filepath: str) -> Tuple[List, List]:
        """解析G代码文件，分离G0和G1指令"""
    
    def execute_g0_instructions(self, g0_instructions: List) -> bool:
        """执行G0指令队列（100%速度，固定ABC角度）"""
    
    def execute_g1_instructions_with_smoothing(self, g1_instructions: List) -> bool:
        """执行G1指令队列（30%速度，带角度平滑处理）"""
```

## 配置参数

### 运动参数
```python
G0_VELOCITY_PERCENT = 100  # G0快速定位速度：100%
G1_VELOCITY_PERCENT = 30   # G1直线插补速度：30%
ACCEL_PERCENT = 20         # 加速度百分比
SMOOTHING_LEVEL = 0        # 平滑度等级
```

### 角度配置
```python
# G0指令固定ABC角度（度）
G0_FIXED_A = 180.0
G0_FIXED_B = 0.0
G0_FIXED_C = 0.0

# 角度平滑处理参数
ANGLE_CHANGE_THRESHOLD = 30.0      # 角度变化阈值（度）
SMOOTH_INTERPOLATION_STEPS = 5     # 角度平滑插值步数
```

## 使用方法

### 1. 基本使用
```python
from improved_gcode_processor import execute_improved_gcode

# 执行改进的G代码处理
success = execute_improved_gcode()
```

### 2. 自定义G代码文件
修改 `improved_gcode_processor.py` 中的 `GCODE_FILE` 变量：
```python
GCODE_FILE = "your_gcode_file.gcode"
```

### 3. 运行测试
```bash
cd test
python test_improved_gcode_config.py
```

## 执行流程

1. **解析G代码文件**: 分离G0和G1指令
2. **连接机械臂**: 建立通信连接
3. **机器人上电**: 检查并执行上电操作
4. **设置坐标系**: 配置用户坐标系UCS1
5. **设置队列模式**: 启用队列运动模式
6. **执行G0指令**: 100%速度，固定ABC角度
7. **处理角度过渡**: G0到G1的角度平滑处理
8. **执行G1指令**: 30%速度，带角度平滑处理
9. **清理和断开**: 安全下电并断开连接

## 角度平滑算法详解

### 检测逻辑
- 计算相邻指令间的角度差值
- 使用最短路径计算（考虑角度的周期性）
- 当任一轴角度变化超过阈值时触发平滑处理

### 插值算法
- 使用线性插值生成中间角度
- 保证角度变化的连续性和平滑性
- 自动处理角度的周期性边界（-180°到180°）

### 示例
```
原始角度变化: A=0° → A=60° (变化60°，超过30°阈值)
平滑处理后: A=0° → A=12° → A=24° → A=36° → A=48° → A=60°
```

## 注意事项

1. **坐标系配置**: 确保 `USER_COORD_NUMBER` 与实际机械臂配置一致
2. **安全检查**: 执行前请确认机械臂工作空间安全
3. **角度范围**: ABC角度值应在机械臂允许范围内
4. **文件格式**: G代码文件应符合标准格式，包含完整的XYZ坐标

## 错误处理

程序包含完整的错误处理机制：
- 连接失败检测
- 上电状态验证
- 队列执行超时处理
- 异常情况下的安全停止

## 性能优化

- 批量队列处理减少通信开销
- 智能角度平滑减少不必要的插值
- 分离G0/G1处理提高执行效率
