#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行模式全局速度设置
验证队列模式下的速度配置是否正确
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def test_mode_speed_settings():
    """测试不同模式下的速度设置"""
    print("🧪 测试运行模式全局速度设置")
    print("=" * 50)
    
    # 模拟连接（不实际连接机械臂）
    socket_fd = -1  # 模拟socket
    
    print("📋 测试步骤说明:")
    print("1. 设置远程模式")
    print("2. 切换到运行模式")
    print("3. 设置运行模式全局速度为100%")
    print("4. 启动队列模式（自动切换到运行模式）")
    print("5. 验证速度设置")
    print()
    
    print("🔧 官方提示解读:")
    print("   '运动队列模式使用的全局速度是运行模式下的全局速度'")
    print("   '上位机开启运动队列模式后，会自动切换成运行模式'")
    print("   '实体示教器上面不会同步更新，实际已经被切换成运行模式'")
    print()
    
    print("💡 解决方案:")
    print("   在启动队列模式前，先切换到运行模式并设置全局速度为100%")
    print("   这样队列模式执行时就会使用100%的全局速度作为基准")
    print("   然后MoveCmd中的velocity参数才能正确生效")
    print()
    
    print("🎯 速度控制层级:")
    print("   全局速度(运行模式) × MoveCmd.velocity% = 实际执行速度")
    print("   例如: 100%(全局) × 60%(G1指令) = 60%实际速度")
    print("        100%(全局) × 100%(G0指令) = 100%实际速度")
    print()
    
    return True

def test_speed_calculation():
    """测试速度计算逻辑"""
    print("🧮 速度计算测试")
    print("=" * 30)
    
    # 测试不同的全局速度设置
    global_speeds = [30, 50, 80, 100]
    g0_velocity = 100  # G0指令速度
    g1_velocity = 60   # G1指令速度
    
    print("全局速度 | G0实际速度 | G1实际速度")
    print("-" * 35)
    
    for global_speed in global_speeds:
        g0_actual = global_speed * g0_velocity / 100
        g1_actual = global_speed * g1_velocity / 100
        print(f"  {global_speed:3d}%   |   {g0_actual:5.1f}%   |   {g1_actual:5.1f}%")
    
    print()
    print("✅ 结论: 全局速度设置为100%时，G0和G1指令的速度差异最明显")
    print("   G0: 100% × 100% = 100% (最快)")
    print("   G1: 100% × 60% = 60% (适中)")
    print()

def test_mode_switching_sequence():
    """测试模式切换序列"""
    print("🔄 模式切换序列测试")
    print("=" * 30)
    
    print("正确的模式切换序列:")
    print("1. 连接机械臂")
    print("2. 上电和基本设置")
    print("3. 设置远程模式 (mode=1)")
    print("4. 切换到运行模式 (mode=2)")
    print("5. 设置运行模式全局速度 (speed=100)")
    print("6. 启动队列模式 (queue_motion_set_status=True)")
    print("   → 此时自动切换到运行模式，使用步骤5设置的全局速度")
    print("7. 执行队列指令")
    print("8. 关闭队列模式")
    print("9. 切换回示教模式")
    print()
    
    print("❌ 错误的做法:")
    print("   在示教模式下设置速度，然后启动队列模式")
    print("   → 队列模式会使用运行模式的速度，而不是示教模式的速度")
    print()
    
    print("✅ 正确的做法:")
    print("   先切换到运行模式，设置全局速度，再启动队列模式")
    print("   → 队列模式会继承运行模式的全局速度设置")
    print()

def compare_with_original():
    """与原始代码对比"""
    print("🔍 与原始代码对比")
    print("=" * 25)
    
    print("原始代码可能的问题:")
    print("❌ 直接在远程模式下启动队列模式")
    print("❌ 没有显式设置运行模式的全局速度")
    print("❌ 依赖默认的运行模式速度设置")
    print()
    
    print("改进后的代码:")
    print("✅ 先切换到运行模式")
    print("✅ 显式设置运行模式全局速度为100%")
    print("✅ 然后启动队列模式")
    print("✅ 确保队列模式使用正确的全局速度")
    print()
    
    print("预期效果:")
    print("🚀 G0指令: 100% × 100% = 100%速度 (快速定位)")
    print("🎯 G1指令: 100% × 60% = 60%速度 (精确加工)")
    print("📈 整体速度显著提升，同时保持G0/G1的速度差异")
    print()

def main():
    """主测试函数"""
    print("🔧 运行模式全局速度配置测试")
    print("=" * 60)
    print()
    
    test_mode_speed_settings()
    test_speed_calculation()
    test_mode_switching_sequence()
    compare_with_original()
    
    print("=" * 60)
    print("🎯 关键修复总结:")
    print()
    print("1. 🔧 问题根源:")
    print("   队列模式使用运行模式的全局速度，而不是示教模式或远程模式的速度")
    print()
    print("2. 🛠️ 解决方案:")
    print("   在启动队列模式前，先切换到运行模式并设置全局速度为100%")
    print()
    print("3. 📈 预期效果:")
    print("   - 整体执行速度显著提升")
    print("   - G0指令达到真正的100%速度")
    print("   - G1指令达到60%速度（相比之前的慢速有明显改善）")
    print("   - 保持G0快速定位和G1精确加工的速度差异")
    print()
    print("4. ✅ 验证方法:")
    print("   运行改进后的improved_gcode_processor.py，观察执行速度是否有明显提升")
    print()

if __name__ == "__main__":
    main()
