#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (XYZ位置验证，使用默认姿态)

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令 (仅X, Y, Z位置，忽略A, B, C角度)。
3. 连接到INEXBOT机械臂。
4. 在指定的用户坐标系下，将G-code路径点转换为机械臂的movel指令并执行。
5. 监控每一步移动，确保完成后再执行下一步。
6. 任务结束后安全下电并断开连接。

注意: 此版本仅验证XYZ位置的可行性，使用固定的默认姿态（工具垂直向下）。
"""

import sys
import os
import time
import re

# 添加lib目录到系统路径 (请根据您的项目结构调整)
# 假设此脚本与您之前的测试脚本在同一目录下
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
#    请将您想要执行的G-code文件放在此脚本的同级目录下
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
#    这个编号必须与G-code路径所基于的坐标系严格对应
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 100  # 速度百分比 (0-100)
ACCEL_PERCENT = 40     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)
TIMEOUT_SECONDS = 60   # 单步运动的超时时间 (秒)

# 4. 默认姿态参数 (工具垂直向下的典型姿态)
# 根据关节角度 J1≈0°, J2≈27°, J3≈-27°, J4≈0°, J5≈90°, J6≈0° 对应的笛卡尔姿态
DEFAULT_RX = 0.0       # 绕X轴旋转角度 (度)
DEFAULT_RY = 0.0       # 绕Y轴旋转角度 (度)
DEFAULT_RZ = 0.0       # 绕Z轴旋转角度 (度)


def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点。
    返回一个包含XYZ位置的字典列表（忽略ABC角度）。
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []

    # 正则表达式用于匹配G0/G1指令中的坐标轴和值（仅XYZ）
    coord_regex = re.compile(r'([XYZ])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                # 忽略注释和空行
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0))
                            # ABC角度将在执行时从当前机械臂姿态获取
                        }
                        path.append(point)
        
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")
        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


# --- 从您提供的测试脚本中复用的核心函数 ---
# (为了代码完整性，这里复制了这些函数。您可以保持它们在外部lib中)

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("  ⏳ 正在等待机器人运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]
            
            if running_status == 0:
                print(" ✅")
                return True
            
            if time.time() - last_print_time > 2: # 每2秒打印一次状态
                 print(".", end="", flush=True)
                 last_print_time = time.time()

            time.sleep(0.1)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(0.5)

    print(" ❌ 超时!")
    return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True
        
        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)
        
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False
        
        time.sleep(1.5)
        
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def get_current_pose(socket_fd):
    """获取机械臂当前位姿"""
    try:
        pos = nrc.VectorDouble()
        # 参数：socketFd, coord(坐标系类型), pos
        # coord: 0=关节，1=直角，2=工具，3=用户
        # 这里使用1=直角坐标系获取笛卡尔位置
        result = nrc.get_current_position(socket_fd, 1, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"❌ 获取当前位姿失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位姿时发生错误: {e}")
        return None


def execute_gcode_on_robot():
    """主执行函数"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 5. 获取当前姿态作为默认姿态（工具垂直向下）
        print("ℹ️ 获取当前机械臂姿态作为默认姿态...")
        current_pose = get_current_pose(socket_fd)
        if current_pose is None:
            print("❌ 无法获取当前姿态，使用预设默认值")
            default_orientation = [DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ]
        else:
            default_orientation = [current_pose[3], current_pose[4], current_pose[5]]
            print(f"✅ 当前姿态: RX={default_orientation[0]:.3f}, RY={default_orientation[1]:.3f}, RZ={default_orientation[2]:.3f}")
            print("   将使用此姿态作为所有路径点的默认姿态")

        # 6. 循环执行路径点
        print("\n" + "=" * 40)
        print(f"🚀 即将开始执行 {len(gcode_path)} 个路径点的移动...")
        print("=" * 40)

        for i, point in enumerate(gcode_path):
            print(f"\n--- 移动到点 {i+1}/{len(gcode_path)} ---")

            # 使用G-code中的XYZ位置，配合获取到的默认姿态
            target_pos = [point['x'], point['y'], point['z'], default_orientation[0], default_orientation[1], default_orientation[2]]

            print(f"  目标位置 (用户坐标系 {USER_COORD_NUMBER}): X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
            print(f"  使用默认姿态: RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")

            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 笛卡尔坐标
            move_cmd.targetPosValue = nrc.VectorDouble()
            for val in target_pos:
                move_cmd.targetPosValue.append(val)
            
            move_cmd.coord = 3  # 用户坐标系类型
            move_cmd.userNum = USER_COORD_NUMBER
            move_cmd.velocity = VELOCITY_PERCENT
            move_cmd.acc = ACCEL_PERCENT
            move_cmd.dec = ACCEL_PERCENT
            move_cmd.pl = SMOOTHING_LEVEL

            # 发送直线运动指令
            result = nrc.robot_movel(socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 移动命令发送失败，错误码: {result}")
                break # 发生错误，终止循环

            # 等待运动完成
            if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
                print("❌ 运动超时，程序终止。")
                break
        
        print("\n" + "=" * 40)
        print("🎉 所有路径点执行完毕！")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 7. 确保安全下电并断开连接
        if socket_fd > 0:
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_on_robot()