#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 改进的G代码处理程序

功能改进：
1. G0指令独立队列处理 - 100%速度，固定ABC角度(A=180°, B=0°, C=0°)
2. G1指令速度控制 - 30%速度执行
3. ABC轴角度平滑处理 - 检测角度突变并实现姿态平滑算法

基于现有的用户坐标系统(UCS1)和队列模式实现
"""

import sys
import os
import time
import re
import math
from typing import List, Dict, Tuple, Optional

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# === 全局配置参数 ===

# G代码文件配置
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1

# 运动参数配置
G0_VELOCITY_PERCENT = 100  # G0快速定位速度：100%
G1_VELOCITY_PERCENT = 30   # G1直线插补速度：30%
ACCEL_PERCENT = 20         # 加速度百分比
SMOOTHING_LEVEL = 0        # 平滑度等级

# G0指令固定ABC角度（度）
G0_FIXED_A = 180.0  # A轴固定角度
G0_FIXED_B = 0.0    # B轴固定角度  
G0_FIXED_C = 0.0    # C轴固定角度

# G1指令默认ABC角度（度）
G1_DEFAULT_A = 0.0
G1_DEFAULT_B = 0.0
G1_DEFAULT_C = 0.0

# 角度偏移量配置
GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = 0.0

# 角度平滑处理参数
ANGLE_CHANGE_THRESHOLD = 30.0  # 角度变化阈值（度），超过此值触发平滑处理
SMOOTH_INTERPOLATION_STEPS = 5  # 角度平滑插值步数

# 队列处理参数
QUEUE_BATCH_SIZE = 20
TIMEOUT_SECONDS = 60

class GCodeInstruction:
    """G代码指令数据结构"""
    def __init__(self, instruction_type: str, x: float, y: float, z: float, 
                 a: Optional[float] = None, b: Optional[float] = None, c: Optional[float] = None):
        self.type = instruction_type  # 'G0' 或 'G1'
        self.x = x
        self.y = y
        self.z = z
        self.a = a
        self.b = b
        self.c = c
        
    def __repr__(self):
        return f"{self.type}(X={self.x:.3f}, Y={self.y:.3f}, Z={self.z:.3f}, A={self.a}, B={self.b}, C={self.c})"

class AngleSmoothing:
    """ABC轴角度平滑处理类"""
    
    @staticmethod
    def normalize_angle_degrees(angle: float) -> float:
        """将角度标准化到 [-180, 180] 范围内"""
        while angle > 180:
            angle -= 360
        while angle <= -180:
            angle += 360
        return angle
    
    @staticmethod
    def calculate_angle_difference(angle1: float, angle2: float) -> float:
        """计算两个角度之间的最小差值"""
        diff = angle2 - angle1
        return AngleSmoothing.normalize_angle_degrees(diff)
    
    @staticmethod
    def detect_angle_jump(prev_angles: Tuple[float, float, float],
                         curr_angles: Tuple[float, float, float],
                         threshold: float = ANGLE_CHANGE_THRESHOLD) -> bool:
        """检测是否存在角度突变"""
        for prev, curr in zip(prev_angles, curr_angles):
            angle_diff = abs(AngleSmoothing.calculate_angle_difference(prev, curr))
            if angle_diff > threshold:
                return True
        return False
    
    @staticmethod
    def generate_smooth_transition(start_angles: Tuple[float, float, float],
                                 end_angles: Tuple[float, float, float],
                                 steps: int = SMOOTH_INTERPOLATION_STEPS) -> List[Tuple[float, float, float]]:
        """生成角度平滑过渡序列"""
        if steps <= 1:
            return [end_angles]
        
        transitions = []
        for i in range(1, steps + 1):
            ratio = i / steps
            interpolated = []
            
            for start, end in zip(start_angles, end_angles):
                # 使用最短路径插值
                diff = AngleSmoothing.calculate_angle_difference(start, end)
                interpolated_angle = start + diff * ratio
                interpolated.append(AngleSmoothing.normalize_angle_degrees(interpolated_angle))
            
            transitions.append(tuple(interpolated))
        
        return transitions

class ImprovedGCodeProcessor:
    """改进的G代码处理器"""
    
    def __init__(self):
        self.socket_fd = -1
        self.g0_queue_size = 0
        self.g1_queue_size = 0
        self.last_g1_angles = None  # 记录上一个G1指令的角度
        
    def parse_gcode_file(self, filepath: str) -> Tuple[List[GCodeInstruction], List[GCodeInstruction]]:
        """
        解析G代码文件，分离G0和G1指令
        返回: (g0_instructions, g1_instructions)
        """
        print(f"📄 正在解析G代码文件: {filepath}")
        g0_instructions = []
        g1_instructions = []
        
        # 正则表达式匹配坐标轴和值
        coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip().upper()
                    
                    # 忽略注释和空行
                    if not line or line.startswith(';'):
                        continue
                    
                    # 检查是否为G0或G1指令
                    if line.startswith('G0') or line.startswith('G1'):
                        coords = dict(coord_regex.findall(line))
                        
                        # 必须包含XYZ坐标
                        if 'X' in coords and 'Y' in coords and 'Z' in coords:
                            instruction_type = 'G0' if line.startswith('G0') else 'G1'
                            
                            # 提取坐标值
                            x = float(coords.get('X', 0.0))
                            y = float(coords.get('Y', 0.0))
                            z = float(coords.get('Z', 0.0))
                            a = float(coords.get('A')) if 'A' in coords else None
                            b = float(coords.get('B')) if 'B' in coords else None
                            c = float(coords.get('C')) if 'C' in coords else None
                            
                            instruction = GCodeInstruction(instruction_type, x, y, z, a, b, c)
                            
                            if instruction_type == 'G0':
                                g0_instructions.append(instruction)
                            else:
                                g1_instructions.append(instruction)
                        else:
                            print(f"⚠️ 第{line_num}行缺少必要的XYZ坐标: {line}")
            
            print(f"✅ 解析完成: G0指令 {len(g0_instructions)} 个, G1指令 {len(g1_instructions)} 个")
            return g0_instructions, g1_instructions
            
        except FileNotFoundError:
            print(f"❌ 错误：G代码文件未找到: {filepath}")
            return [], []
        except Exception as e:
            print(f"❌ 解析G代码文件时发生错误: {e}")
            return [], []
    
    def get_instruction_angles(self, instruction: GCodeInstruction, is_g0: bool = False) -> Tuple[float, float, float]:
        """获取指令的ABC角度（度）"""
        if is_g0:
            # G0指令使用固定角度
            return (G0_FIXED_A, G0_FIXED_B, G0_FIXED_C)
        else:
            # G1指令使用指定角度或默认角度
            a = instruction.a if instruction.a is not None else G1_DEFAULT_A
            b = instruction.b if instruction.b is not None else G1_DEFAULT_B
            c = instruction.c if instruction.c is not None else G1_DEFAULT_C
            return (a, b, c)
    
    def convert_to_robot_angles(self, gcode_angles: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """将G代码角度转换为机械臂角度（加上偏移量）"""
        a, b, c = gcode_angles
        robot_a = AngleSmoothing.normalize_angle_degrees(a + GCODE_TO_ROBOT_OFFSET_A)
        robot_b = AngleSmoothing.normalize_angle_degrees(b + GCODE_TO_ROBOT_OFFSET_B)
        robot_c = AngleSmoothing.normalize_angle_degrees(c + GCODE_TO_ROBOT_OFFSET_C)
        return (robot_a, robot_b, robot_c)
    
    def create_move_command(self, instruction: GCodeInstruction, angles_deg: Tuple[float, float, float], 
                          velocity_percent: int) -> nrc.MoveCmd:
        """创建运动命令"""
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = nrc.PosType_data
        move_cmd.targetPosValue = nrc.VectorDouble()
        
        # 添加位置坐标（XYZ）
        move_cmd.targetPosValue.append(instruction.x)
        move_cmd.targetPosValue.append(instruction.y)
        move_cmd.targetPosValue.append(instruction.z)
        
        # 添加姿态角度（转换为弧度）
        for angle_deg in angles_deg:
            move_cmd.targetPosValue.append(math.radians(angle_deg))
        
        # 设置运动参数
        move_cmd.coord = 3  # 用户坐标系
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL
        
        return move_cmd

    def setup_queue_mode(self) -> bool:
        """设置队列模式"""
        print("🔧 正在设置队列模式...")

        try:
            # 1. 设置远程模式
            result = nrc.set_current_mode(self.socket_fd, 1)  # 1 = 远程模式
            if result != 0:
                print(f"❌ 设置远程模式失败，错误码: {result}")
                return False
            print("✅ 远程模式设置成功")
            time.sleep(0.5)

            # 2. 启动队列模式
            result = nrc.queue_motion_set_status(self.socket_fd, True)
            if result != 0:
                print(f"❌ 启动队列模式失败，错误码: {result}")
                return False
            print("✅ 队列模式启动成功")
            time.sleep(0.5)

            # 3. 清除队列数据
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清除队列数据失败，错误码: {result}")
                return False
            print("✅ 队列数据清除成功")

            # 重置队列大小计数器
            self.g0_queue_size = 0
            self.g1_queue_size = 0
            return True

        except Exception as e:
            print(f"❌ 设置队列模式时发生错误: {e}")
            return False

    def cleanup_queue_mode(self):
        """清理队列模式"""
        print("🧹 正在清理队列模式...")

        try:
            # 关闭队列模式
            result = nrc.queue_motion_set_status(self.socket_fd, False)
            if result != 0:
                print(f"⚠️ 关闭队列模式失败，错误码: {result}")
            else:
                print("✅ 队列模式已关闭")

            # 设置回示教模式
            result = nrc.set_current_mode(self.socket_fd, 0)  # 0 = 示教模式
            if result != 0:
                print(f"⚠️ 设置示教模式失败，错误码: {result}")
            else:
                print("✅ 已切换回示教模式")

        except Exception as e:
            print(f"⚠️ 清理队列模式时发生错误: {e}")

    def execute_g0_instructions(self, g0_instructions: List[GCodeInstruction]) -> bool:
        """执行G0指令队列（快速定位，100%速度，固定ABC角度）"""
        if not g0_instructions:
            print("ℹ️ 没有G0指令需要执行")
            return True

        print(f"\n🚀 开始执行 {len(g0_instructions)} 个G0快速定位指令")
        print(f"   速度: {G0_VELOCITY_PERCENT}%")
        print(f"   固定角度: A={G0_FIXED_A}°, B={G0_FIXED_B}°, C={G0_FIXED_C}°")

        try:
            # 清空队列
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清空G0队列失败，错误码: {result}")
                return False

            self.g0_queue_size = 0

            # 添加所有G0指令到队列
            for i, instruction in enumerate(g0_instructions):
                print(f"📝 添加G0指令 {i+1}/{len(g0_instructions)}: X={instruction.x:.3f}, Y={instruction.y:.3f}, Z={instruction.z:.3f}")

                # 获取固定的ABC角度
                gcode_angles = self.get_instruction_angles(instruction, is_g0=True)
                robot_angles = self.convert_to_robot_angles(gcode_angles)

                # 创建运动命令
                move_cmd = self.create_move_command(instruction, robot_angles, G0_VELOCITY_PERCENT)

                # 添加到队列
                result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
                if result != 0:
                    print(f"❌ 添加G0指令到队列失败，错误码: {result}")
                    return False

                self.g0_queue_size += 1
                print(f"    ✅ 成功添加到队列，当前队列大小: {self.g0_queue_size}")

            # 发送队列到控制器并等待执行完成
            return self.send_queue_and_wait("G0")

        except Exception as e:
            print(f"❌ 执行G0指令时发生错误: {e}")
            return False

    def execute_g1_instructions_with_smoothing(self, g1_instructions: List[GCodeInstruction]) -> bool:
        """执行G1指令队列（30%速度，带角度平滑处理）"""
        if not g1_instructions:
            print("ℹ️ 没有G1指令需要执行")
            return True

        print(f"\n🎯 开始执行 {len(g1_instructions)} 个G1直线插补指令")
        print(f"   速度: {G1_VELOCITY_PERCENT}%")
        print("   启用ABC轴角度平滑处理")

        try:
            # 清空队列
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清空G1队列失败，错误码: {result}")
                return False

            self.g1_queue_size = 0
            processed_instructions = []

            # 处理G1指令序列，检测角度突变并插入平滑过渡
            for i, instruction in enumerate(g1_instructions):
                print(f"📝 处理G1指令 {i+1}/{len(g1_instructions)}: X={instruction.x:.3f}, Y={instruction.y:.3f}, Z={instruction.z:.3f}")

                # 获取当前指令的角度
                current_gcode_angles = self.get_instruction_angles(instruction, is_g0=False)
                current_robot_angles = self.convert_to_robot_angles(current_gcode_angles)

                # 检查是否需要角度平滑处理
                if self.last_g1_angles is not None:
                    if AngleSmoothing.detect_angle_jump(self.last_g1_angles, current_robot_angles):
                        print(f"    🔄 检测到角度突变，插入平滑过渡")
                        print(f"       从: A={self.last_g1_angles[0]:.1f}°, B={self.last_g1_angles[1]:.1f}°, C={self.last_g1_angles[2]:.1f}°")
                        print(f"       到: A={current_robot_angles[0]:.1f}°, B={current_robot_angles[1]:.1f}°, C={current_robot_angles[2]:.1f}°")

                        # 生成平滑过渡序列
                        smooth_transitions = AngleSmoothing.generate_smooth_transition(
                            self.last_g1_angles, current_robot_angles, SMOOTH_INTERPOLATION_STEPS
                        )

                        # 为每个过渡步骤创建中间指令（保持当前位置，只改变角度）
                        for j, transition_angles in enumerate(smooth_transitions[:-1]):  # 最后一个角度由原指令处理
                            print(f"       插入过渡步骤 {j+1}/{len(smooth_transitions)-1}: A={transition_angles[0]:.1f}°, B={transition_angles[1]:.1f}°, C={transition_angles[2]:.1f}°")

                            # 创建过渡指令（位置不变，只改变角度）
                            transition_cmd = self.create_move_command(instruction, transition_angles, G1_VELOCITY_PERCENT)
                            processed_instructions.append(transition_cmd)

                # 添加原始指令
                move_cmd = self.create_move_command(instruction, current_robot_angles, G1_VELOCITY_PERCENT)
                processed_instructions.append(move_cmd)

                # 更新上一个角度记录
                self.last_g1_angles = current_robot_angles

                print(f"    ✅ 指令处理完成，角度: A={current_robot_angles[0]:.1f}°, B={current_robot_angles[1]:.1f}°, C={current_robot_angles[2]:.1f}°")

            # 将所有处理后的指令添加到队列
            print(f"\n📦 添加 {len(processed_instructions)} 个指令到G1队列（包含平滑过渡）")
            for i, move_cmd in enumerate(processed_instructions):
                result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
                if result != 0:
                    print(f"❌ 添加G1指令到队列失败，错误码: {result}")
                    return False

                self.g1_queue_size += 1
                if (i + 1) % 10 == 0:  # 每10个指令打印一次进度
                    print(f"    📝 已添加 {i+1}/{len(processed_instructions)} 个指令到队列")

            print(f"✅ 所有G1指令已添加到队列，队列大小: {self.g1_queue_size}")

            # 发送队列到控制器并等待执行完成
            return self.send_queue_and_wait("G1")

        except Exception as e:
            print(f"❌ 执行G1指令时发生错误: {e}")
            return False

    def send_queue_and_wait(self, queue_type: str) -> bool:
        """发送队列并等待执行完成"""
        queue_size = self.g0_queue_size if queue_type == "G0" else self.g1_queue_size

        if queue_size == 0:
            print("⚠️ 队列为空，无需发送")
            return True

        try:
            print(f"📤 正在发送{queue_type}队列到控制器，队列大小: {queue_size}")

            # 发送队列到控制器
            result = nrc.queue_motion_send_to_controller(self.socket_fd, queue_size)
            if result != 0:
                print(f"❌ 发送{queue_type}队列失败，错误码: {result}")
                return False

            print(f"✅ {queue_type}队列发送成功，开始执行...")
            time.sleep(0.5)

            # 等待队列执行完成
            print(f"⏳ 正在等待{queue_type}队列执行完成...", end="", flush=True)
            start_time = time.time()
            last_print_time = 0
            timeout = TIMEOUT_SECONDS * queue_size

            while time.time() - start_time < timeout:
                try:
                    # 检查机器人运行状态
                    running_status = 0
                    result = nrc.get_robot_running_state(self.socket_fd, running_status)
                    if isinstance(result, list) and len(result) > 1:
                        running_status = result[1]

                    # 检查队列长度
                    queue_len = 0
                    result = nrc.queue_motion_get_queuelen(self.socket_fd, queue_len)
                    if isinstance(result, list) and len(result) > 1:
                        queue_len = result[1]

                    # 如果机器人停止运行且队列为空，说明执行完成
                    if running_status == 0 and queue_len == 0:
                        print(" ✅")
                        print(f"🎉 {queue_type}队列执行完成！")
                        return True

                    # 定期打印状态
                    if time.time() - last_print_time > 2:
                        print(".", end="", flush=True)
                        last_print_time = time.time()

                    time.sleep(0.1)

                except Exception as e:
                    print(f"\n⚠️ 检查执行状态时发生错误: {e}")
                    time.sleep(0.5)

            print(" ❌ 超时!")
            print(f"❌ {queue_type}队列执行超时 ({timeout}秒)")
            return False

        except Exception as e:
            print(f"❌ 发送{queue_type}队列时发生错误: {e}")
            return False

    def handle_g0_to_g1_transition(self, last_g0_instruction: Optional[GCodeInstruction],
                                  first_g1_instruction: GCodeInstruction) -> List[nrc.MoveCmd]:
        """处理G0到G1的角度过渡"""
        if last_g0_instruction is None:
            return []

        # 获取G0的固定角度和G1的目标角度
        g0_gcode_angles = self.get_instruction_angles(last_g0_instruction, is_g0=True)
        g0_robot_angles = self.convert_to_robot_angles(g0_gcode_angles)

        g1_gcode_angles = self.get_instruction_angles(first_g1_instruction, is_g0=False)
        g1_robot_angles = self.convert_to_robot_angles(g1_gcode_angles)

        # 检查是否需要角度平滑处理
        if AngleSmoothing.detect_angle_jump(g0_robot_angles, g1_robot_angles):
            print(f"🔄 检测到G0到G1的角度突变，插入平滑过渡")
            print(f"   G0角度: A={g0_robot_angles[0]:.1f}°, B={g0_robot_angles[1]:.1f}°, C={g0_robot_angles[2]:.1f}°")
            print(f"   G1角度: A={g1_robot_angles[0]:.1f}°, B={g1_robot_angles[1]:.1f}°, C={g1_robot_angles[2]:.1f}°")

            # 生成平滑过渡序列
            smooth_transitions = AngleSmoothing.generate_smooth_transition(
                g0_robot_angles, g1_robot_angles, SMOOTH_INTERPOLATION_STEPS
            )

            # 创建过渡指令（在G0的最后位置进行角度过渡）
            transition_commands = []
            for i, transition_angles in enumerate(smooth_transitions[:-1]):  # 最后一个角度由G1指令处理
                print(f"   插入G0→G1过渡步骤 {i+1}/{len(smooth_transitions)-1}: A={transition_angles[0]:.1f}°, B={transition_angles[1]:.1f}°, C={transition_angles[2]:.1f}°")

                # 在G0的最后位置进行角度过渡
                transition_cmd = self.create_move_command(last_g0_instruction, transition_angles, G1_VELOCITY_PERCENT)
                transition_commands.append(transition_cmd)

            return transition_commands

        return []

# === 机器人控制辅助函数 ===

def robot_power_on_if_needed(socket_fd: int) -> bool:
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]

        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True

        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)

        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False

        time.sleep(1.5)

        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd: int) -> bool:
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def execute_improved_gcode():
    """主执行函数 - 改进的G代码处理"""
    print("=" * 80)
    print("INEXBOT机械臂 改进的G代码处理程序")
    print("=" * 80)
    print("功能特性:")
    print("  🚀 G0指令独立队列处理 - 100%速度，固定ABC角度")
    print("  🎯 G1指令速度控制 - 30%速度执行")
    print("  🔄 ABC轴角度平滑处理 - 防止旋转轴速度突变")
    print("=" * 80)

    processor = ImprovedGCodeProcessor()

    try:
        # 1. 解析G代码文件
        g0_instructions, g1_instructions = processor.parse_gcode_file(GCODE_FILE)
        if not g0_instructions and not g1_instructions:
            print("❌ 没有找到有效的G代码指令")
            return False

        # 2. 连接机械臂
        print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        processor.socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if processor.socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        print(f"✅ 连接成功！Socket ID: {processor.socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(processor.socket_fd):
            return False

        # 4. 设置用户坐标系
        print(f"\nℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(processor.socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return False
        time.sleep(0.2)

        # 5. 设置队列模式
        if not processor.setup_queue_mode():
            print("❌ 队列模式设置失败，程序终止")
            return False

        # 6. 执行G0指令（如果有）
        if g0_instructions:
            success = processor.execute_g0_instructions(g0_instructions)
            if not success:
                print("❌ G0指令执行失败")
                return False

        # 7. 处理G0到G1的角度过渡（如果两种指令都存在）
        if g0_instructions and g1_instructions:
            print("\n🔄 处理G0到G1的角度过渡...")
            transition_commands = processor.handle_g0_to_g1_transition(
                g0_instructions[-1], g1_instructions[0]
            )

            if transition_commands:
                # 清空队列并添加过渡指令
                result = nrc.queue_motion_clear_Data(processor.socket_fd)
                if result == 0:
                    for cmd in transition_commands:
                        nrc.queue_motion_push_back_moveL(processor.socket_fd, cmd)

                    # 发送过渡指令
                    result = nrc.queue_motion_send_to_controller(processor.socket_fd, len(transition_commands))
                    if result == 0:
                        print("✅ G0→G1角度过渡指令执行中...")
                        # 等待过渡完成
                        processor.send_queue_and_wait("过渡")

        # 8. 执行G1指令（如果有）
        if g1_instructions:
            success = processor.execute_g1_instructions_with_smoothing(g1_instructions)
            if not success:
                print("❌ G1指令执行失败")
                return False

        print("\n" + "=" * 80)
        print("🎉 改进的G代码处理完成！")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
        return False

    finally:
        # 清理和断开连接
        if processor.socket_fd > 0:
            # 清理队列模式
            processor.cleanup_queue_mode()

            # 安全下电并断开连接
            robot_power_off(processor.socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(processor.socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_improved_gcode()
