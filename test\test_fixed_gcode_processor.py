#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的G代码处理器测试脚本
基于原始run_gcode.py的成功配置，验证改进功能
"""

import sys
import os

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from improved_gcode_processor import ImprovedGCodeProcessor, execute_improved_gcode

def test_angle_conversion():
    """测试角度转换逻辑"""
    print("=== 测试角度转换逻辑 ===")
    
    processor = ImprovedGCodeProcessor()
    
    # 测试G0固定角度
    print("G0固定角度测试:")
    from improved_gcode_processor import GCodeInstruction
    g0_instruction = GCodeInstruction("G0", 10.0, 20.0, 30.0)
    
    # 获取G0的G-code角度
    g0_gcode_angles = processor.get_instruction_angles(g0_instruction, is_g0=True)
    print(f"  G0 G-code角度: A={g0_gcode_angles[0]}°, B={g0_gcode_angles[1]}°, C={g0_gcode_angles[2]}°")
    
    # 转换为机械臂角度
    g0_robot_angles = processor.convert_to_robot_angles(g0_gcode_angles)
    print(f"  G0 机械臂角度: RX={g0_robot_angles[0]}°, RY={g0_robot_angles[1]}°, RZ={g0_robot_angles[2]}°")
    
    # 测试G1角度
    print("\nG1角度测试:")
    g1_instruction = GCodeInstruction("G1", 15.0, 25.0, 35.0, 45.0, 30.0, 15.0)
    
    # 获取G1的G-code角度
    g1_gcode_angles = processor.get_instruction_angles(g1_instruction, is_g0=False)
    print(f"  G1 G-code角度: A={g1_gcode_angles[0]}°, B={g1_gcode_angles[1]}°, C={g1_gcode_angles[2]}°")
    
    # 转换为机械臂角度
    g1_robot_angles = processor.convert_to_robot_angles(g1_gcode_angles)
    print(f"  G1 机械臂角度: RX={g1_robot_angles[0]}°, RY={g1_robot_angles[1]}°, RZ={g1_robot_angles[2]}°")
    
    print()

def test_gcode_parsing():
    """测试G代码解析功能"""
    print("=== 测试G代码解析功能 ===")
    
    processor = ImprovedGCodeProcessor()
    
    # 使用测试G代码文件
    test_file = "test_improved_gcode.gcode"
    if os.path.exists(test_file):
        g0_instructions, g1_instructions = processor.parse_gcode_file(test_file)
        
        print(f"解析结果:")
        print(f"  G0指令数量: {len(g0_instructions)}")
        print(f"  G1指令数量: {len(g1_instructions)}")
        
        if g0_instructions:
            print(f"  第一个G0指令: {g0_instructions[0]}")
        
        if g1_instructions:
            print(f"  第一个G1指令: {g1_instructions[0]}")
    else:
        print(f"测试文件 {test_file} 不存在")
    
    print()

def compare_with_original():
    """与原始run_gcode.py的配置进行对比"""
    print("=== 与原始run_gcode.py配置对比 ===")
    
    from improved_gcode_processor import (
        G0_FIXED_A, G0_FIXED_B, G0_FIXED_C,
        G1_DEFAULT_A, G1_DEFAULT_B, G1_DEFAULT_C,
        GCODE_TO_ROBOT_OFFSET_A, GCODE_TO_ROBOT_OFFSET_B, GCODE_TO_ROBOT_OFFSET_C
    )
    
    print("配置参数对比:")
    print(f"  G0固定角度: A={G0_FIXED_A}°, B={G0_FIXED_B}°, C={G0_FIXED_C}°")
    print(f"  G1默认角度: A={G1_DEFAULT_A}°, B={G1_DEFAULT_B}°, C={G1_DEFAULT_C}°")
    print(f"  角度偏移量: A+{GCODE_TO_ROBOT_OFFSET_A}°, B+{GCODE_TO_ROBOT_OFFSET_B}°, C+{GCODE_TO_ROBOT_OFFSET_C}°")
    
    # 计算最终的机械臂角度
    g0_final_rx = G0_FIXED_A + GCODE_TO_ROBOT_OFFSET_A
    g0_final_ry = G0_FIXED_B + GCODE_TO_ROBOT_OFFSET_B
    g0_final_rz = G0_FIXED_C + GCODE_TO_ROBOT_OFFSET_C
    
    g1_final_rx = G1_DEFAULT_A + GCODE_TO_ROBOT_OFFSET_A
    g1_final_ry = G1_DEFAULT_B + GCODE_TO_ROBOT_OFFSET_B
    g1_final_rz = G1_DEFAULT_C + GCODE_TO_ROBOT_OFFSET_C
    
    print(f"  G0最终机械臂角度: RX={g0_final_rx}°, RY={g0_final_ry}°, RZ={g0_final_rz}°")
    print(f"  G1默认机械臂角度: RX={g1_final_rx}°, RY={g1_final_ry}°, RZ={g1_final_rz}°")
    
    print("\n✅ 配置与原始run_gcode.py一致!")
    print()

def main():
    """主测试函数"""
    print("🔧 修复后的G代码处理器测试")
    print("=" * 50)
    
    test_angle_conversion()
    test_gcode_parsing()
    compare_with_original()
    
    print("=" * 50)
    print("🎯 关键修复点:")
    print("1. ✅ G0固定角度改为G-code坐标系中的0°,0°,0°")
    print("2. ✅ 角度偏移量与原始run_gcode.py完全一致")
    print("3. ✅ 运动命令参数设置与原始代码一致")
    print("4. ✅ 坐标系配置(coord=3, userNum=1)保持一致")
    print()
    
    # 询问是否运行完整测试
    try:
        response = input("是否运行完整的机械臂测试? (y/n): ").strip().lower()
        if response == 'y':
            print("\n🚀 开始运行完整的改进G代码处理功能...")
            success = execute_improved_gcode()
            if success:
                print("✅ 完整测试成功!")
            else:
                print("❌ 完整测试失败!")
        else:
            print("跳过完整测试")
    except KeyboardInterrupt:
        print("\n测试被用户中断")

if __name__ == "__main__":
    main()
