#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (解耦控制模式) - v6 最终版

功能:
1.  G-code文件解析为G0(快速移动)和G1(工作移动)的运动段。
2.  G0段：使用100%速度和固定安全姿态进行快速定位。
3.  G1段：使用MOVL忠实执行G-code的直线几何路径。
4.  姿态解耦控制（先移动，后旋转）：
    - 检测到姿态变化超过阈值时，将运动分解为两步：
      a. 保持起始姿态，直线移动到目标位置。
      b. 到达目标位置后，原地旋转到目标姿态。
    - 该策略100%保证了G-code的几何路径精度。
"""

import sys
import os
import time
import re
import math
import numpy as np
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
try:
    from config import ROBOT_IP, ROBOT_PORT
    import nrc_interface as nrc
except ImportError:
    print("❌ 错误: 无法导入 nrc_interface 或 config。请确保文件路径正确。")
    sys.exit(1)

# --- 全局参数配置 ---
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1
G0_VELOCITY, G0_ACCEL, G0_SMOOTHING = 100, 50, 0
G1_VELOCITY, G1_ACCEL, G1_SMOOTHING = 30, 20, 0
G0_FIXED_POSE_DEG = [180.0, 0.0, 0.0]
GCODE_DEFAULT_A, GCODE_DEFAULT_B, GCODE_DEFAULT_C = 0.0, 0.0, 0.0
GCODE_TO_ROBOT_OFFSET_A, GCODE_TO_ROBOT_OFFSET_B, GCODE_TO_ROBOT_OFFSET_C = 180.0, 0.0, 0.0

# --- 姿态平滑与队列限制配置 ---
POSE_CHANGE_THRESHOLD_DEG = 15.0  # 姿态变化阈值(度)，超过此值则分解运动
CONTROLLER_QUEUE_LIMIT = 20

# --- 辅助函数 (与v5版本基本相同，gcode_point_to_pose_deg有改进) ---
def normalize_angle_degrees(angle):
    while angle > 180: angle -= 360
    while angle <= -180: angle += 360
    return angle

def parse_gcode_to_segments(filepath):
    # (此函数与之前版本相同)
    print(f"📄 正在解析G-code并分段: {filepath}")
    segments = []
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                cmd_type = None
                if line.startswith('G0'): cmd_type = 'G0'
                elif line.startswith('G1'): cmd_type = 'G1'
                if cmd_type:
                    coords = dict(coord_regex.findall(line))
                    if all(k in coords for k in ('X', 'Y', 'Z')):
                        point = {'x': float(coords.get('X')),'y': float(coords.get('Y')),'z': float(coords.get('Z')),'a': float(coords.get('A')) if 'A' in coords else None,'b': float(coords.get('B')) if 'B' in coords else None,'c': float(coords.get('C')) if 'C' in coords else None}
                        if not segments or segments[-1]['type'] != cmd_type:
                            segments.append({'type': cmd_type, 'points': [point]})
                        else:
                            segments[-1]['points'].append(point)
        print(f"✅ 解析完成，共找到 {len(segments)} 个运动段。")
        return segments
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}"); return None

def gcode_point_to_pose_deg(point, last_known_pose_deg, use_fixed_pose=False):
    """从G-code点计算出目标姿态(度)，如果G-code未提供则沿用上一姿态"""
    if use_fixed_pose:
        return G0_FIXED_POSE_DEG
    
    # 如果G-code未指定角度，则沿用上一个点的角度
    gcode_a = point['a'] if point['a'] is not None else (last_known_pose_deg[0] - GCODE_TO_ROBOT_OFFSET_A)
    gcode_b = point['b'] if point['b'] is not None else (last_known_pose_deg[1] - GCODE_TO_ROBOT_OFFSET_B)
    gcode_c = point['c'] if point['c'] is not None else (last_known_pose_deg[2] - GCODE_TO_ROBOT_OFFSET_C)
    
    rx = normalize_angle_degrees(gcode_a + GCODE_TO_ROBOT_OFFSET_A)
    ry = normalize_angle_degrees(gcode_b + GCODE_TO_ROBOT_OFFSET_B)
    rz = normalize_angle_degrees(gcode_c + GCODE_TO_ROBOT_OFFSET_C)
    return [rx, ry, rz]

def create_command_from_pose(position_xyz, pose_deg, velocity, accel, smoothing):
    # (此函数与之前版本相同)
    pose_rad = [math.radians(angle) for angle in pose_deg]
    target_pose = position_xyz + pose_rad
    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = nrc.VectorDouble()
    for val in target_pose: cmd.targetPosValue.append(val)
    cmd.coord = 3; cmd.userNum = USER_COORD_NUMBER
    cmd.velocity = velocity; cmd.acc = accel; cmd.dec = accel
    cmd.pl = smoothing
    return cmd

# --- 机器人控制与执行函数 ---
def initialize_robot(socket_fd): # (不变)
    print("\n🔧 正在初始化机器人...")
    if not robot_power_on_if_needed(socket_fd): return False
    if nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER) != 0:
        print(f"❌ 设置用户坐标系失败"); return False
    if nrc.set_current_mode(socket_fd, 1) != 0:
        print("❌ 设置远程模式失败"); return False
    print("✅ 机器人初始化成功。")
    return True

def setup_queue_mode(socket_fd): # (不变)
    print("\n🔄 正在设置队列模式...")
    if nrc.queue_motion_set_status(socket_fd, True) != 0:
        print("❌ 启用队列模式失败"); return False
    if nrc.queue_motion_clear_Data(socket_fd) != 0:
        print("❌ 清空队列数据失败"); return False
    print("✅ 队列模式已启用并清空。")
    return True

def wait_for_batch_complete(socket_fd, timeout_seconds=300): # (不变)
    print(f"  ⏳ 等待批次执行完成...", end="", flush=True)
    start_time = time.time()
    while time.time() - start_time < timeout_seconds:
        try:
            res_state = nrc.get_robot_running_state(socket_fd, 0)
            res_len = nrc.queue_motion_get_queuelen(socket_fd, 0)
            is_stopped = isinstance(res_state, list) and res_state[1] == 0
            is_empty = isinstance(res_len, list) and res_len[1] == 0
            if is_stopped and is_empty:
                print(" ✅"); return True
            time.sleep(0.2); print(".", end="", flush=True)
        except Exception as e:
            print(f"\n⚠️ 检查状态时出错: {e}"); time.sleep(1)
    print(" ❌ 超时!"); return False

def execute_g0_segment(socket_fd, segment): # (不变)
    point = segment['points'][0]
    print(f"  ⚡️ 执行G0快速定位到 X:{point['x']:.2f} Y:{point['y']:.2f} Z:{point['z']:.2f}")
    pose_deg = gcode_point_to_pose_deg(point, None, use_fixed_pose=True)
    cmd = create_command_from_pose([point['x'], point['y'], point['z']], pose_deg, G0_VELOCITY, G0_ACCEL, G0_SMOOTHING)
    nrc.queue_motion_clear_Data(socket_fd)
    if nrc.queue_motion_push_back_moveL(socket_fd, cmd) != 0: raise RuntimeError("添加G0指令失败")
    if nrc.queue_motion_send_to_controller(socket_fd, 1) != 0: raise RuntimeError("发送G0指令失败")
    if not wait_for_batch_complete(socket_fd): raise RuntimeError("G0运动执行失败")
    return {'point': point, 'pose_deg': pose_deg}

# ---------- v6 核心逻辑：解耦控制 ----------
def execute_g1_segment_decoupled(socket_fd, segment, last_point_state):
    points = segment['points']
    if not points: return last_point_state
    print(f"  📏 执行G1直线插补（解耦模式），包含 {len(points)} 个路径点...")
    
    all_commands = []
    current_state = last_point_state

    for point in points:
        target_pos_xyz = [point['x'], point['y'], point['z']]
        target_pose_deg = gcode_point_to_pose_deg(point, current_state['pose_deg'])

        pose_change = np.abs(np.array(target_pose_deg) - np.array(current_state['pose_deg']))
        for i in range(3):
            if pose_change[i] > 180: pose_change[i] = 360 - pose_change[i]
        
        # 检查是否需要分解运动
        if np.any(pose_change > POSE_CHANGE_THRESHOLD_DEG):
            print(f"\n    ⚠️  检测到姿态突变 > {POSE_CHANGE_THRESHOLD_DEG:.1f}°，分解为“先移动，后旋转”...")
            
            # 1. 第一步：移动到目标位置，但保持当前姿态
            print("      a. 直线移动...")
            cmd_move = create_command_from_pose(target_pos_xyz, current_state['pose_deg'], G1_VELOCITY, G1_ACCEL, G1_SMOOTHING)
            all_commands.append(cmd_move)
            
            # 2. 第二步：原地旋转到目标姿态
            print("      b. 原地旋转...")
            cmd_rotate = create_command_from_pose(target_pos_xyz, target_pose_deg, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING)
            all_commands.append(cmd_rotate)
        else:
            # 如果姿态变化不大，则直接生成一条指令
            cmd_normal = create_command_from_pose(target_pos_xyz, target_pose_deg, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING)
            all_commands.append(cmd_normal)

        current_state = {'point': point, 'pose_deg': target_pose_deg}
    
    # 将所有生成的指令按队列限制分块发送
    print(f"  ℹ️ 总共生成 {len(all_commands)} 条指令，将按每批次最多 {CONTROLLER_QUEUE_LIMIT} 条发送。")
    total_cmds = len(all_commands)
    for i in range(0, total_cmds, CONTROLLER_QUEUE_LIMIT):
        batch = all_commands[i : i + CONTROLLER_QUEUE_LIMIT]
        print(f"\n    📦 发送子批次: 指令 {i+1}-{i+len(batch)} / {total_cmds}")
        nrc.queue_motion_clear_Data(socket_fd)
        for cmd in batch:
            if nrc.queue_motion_push_back_moveL(socket_fd, cmd) != 0: raise RuntimeError("添加MOVL指令失败")
        if nrc.queue_motion_send_to_controller(socket_fd, len(batch)) != 0: raise RuntimeError("发送子批次失败")
        if not wait_for_batch_complete(socket_fd): raise RuntimeError("子批次执行失败")
        print(f"    ✅ 子批次执行成功。")
        
    return current_state
# ----------------------------------------

def run_gcode_decoupled():
    """主执行函数"""
    print("=" * 60); print("INEXBOT机械臂 G-code 路径执行程序 (解耦控制模式 - v6)"); print("=" * 60)
    segments = parse_gcode_to_segments(GCODE_FILE)
    if not segments: return
    socket_fd = -1
    try:
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0: raise ConnectionError("连接失败！")
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        if not initialize_robot(socket_fd): raise RuntimeError("机器人初始化失败")
        if not setup_queue_mode(socket_fd): raise RuntimeError("队列模式设置失败")

        print("\n" + "=" * 40); print(f"🚀 开始按段执行 {len(segments)} 个G-code运动段..."); print("=" * 40)
        
        last_point_state = {'point': {'x':0,'y':0,'z':0}, 'pose_deg': G0_FIXED_POSE_DEG}
        
        for i, segment in enumerate(segments):
            print(f"\n--- 段 {i+1}/{len(segments)} (类型: {segment['type']}) ---")
            if segment['type'] == 'G0':
                last_point_state = execute_g0_segment(socket_fd, segment)
            elif segment['type'] == 'G1':
                last_point_state = execute_g1_segment_decoupled(socket_fd, segment, last_point_state)
            print(f"✅ 段 {i+1} 执行完成。")

        print("\n" + "=" * 40); print("🎉 所有G-code路径执行完毕！"); print("=" * 40)
    except (ConnectionError, RuntimeError, Exception) as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        if socket_fd > 0:
            print("\n程序结束，正在安全关闭...")
            nrc.queue_motion_set_status(socket_fd, False)
            robot_power_off(socket_fd)
            print("🔌 正在断开连接..."); nrc.disconnect_robot(socket_fd); print("✅ 连接已断开。")

def robot_power_on_if_needed(socket_fd):
    try:
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人伺服已上电。"); return True
        print("ℹ️ 机器人需要上电...")
        nrc.clear_error(socket_fd); time.sleep(0.2)
        nrc.set_servo_state(socket_fd, 1); time.sleep(0.2)
        if nrc.set_servo_poweron(socket_fd) != 0:
            print(f"❌ 上电失败！"); return False
        time.sleep(1.5)
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人上电成功！"); return True
        print(f"❌ 上电后状态异常: {res}"); return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}"); return False

def robot_power_off(socket_fd):
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd); time.sleep(1); print("✅ 机器人已下电。")
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")

if __name__ == "__main__":
    run_gcode_decoupled()