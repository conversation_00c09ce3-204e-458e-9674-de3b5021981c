#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (精密队列模式) - v3 最终版

功能:
1.  将G-code文件解析为G0(快速移动)和G1(工作移动)的运动段。
2.  G0段：使用100%速度和固定安全姿态进行快速定位。
3.  G1段：默认使用MOVL忠实执行G-code路径。
4.  姿态突变检测与平滑：
    - 在G0->G1过渡或G1段内部检测姿态角度的大幅变化。
    - 当变化超过阈值时，自动在两个点之间插入中间点，用一系列短MOVL实现姿态的平滑过渡。
    - 确保了路径的几何精度和运动的平稳性。
"""

import sys
import os
import time
import re
import math
import numpy as np
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
try:
    from config import ROBOT_IP, ROBOT_PORT
    import nrc_interface as nrc
except ImportError:
    print("❌ 错误: 无法导入 nrc_interface 或 config。请确保文件路径正确。")
    sys.exit(1)

# --- 全局参数配置 ---
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1
G0_VELOCITY, G0_ACCEL, G0_SMOOTHING = 100, 50, 0
G1_VELOCITY, G1_ACCEL, G1_SMOOTHING = 30, 20, 0
G0_FIXED_POSE_DEG = [180.0, 0.0, 0.0]
GCODE_DEFAULT_A, GCODE_DEFAULT_B, GCODE_DEFAULT_C = 0.0, 0.0, 0.0
GCODE_TO_ROBOT_OFFSET_A, GCODE_TO_ROBOT_OFFSET_B, GCODE_TO_ROBOT_OFFSET_C = 180.0, 0.0, 0.0

# --- 姿态平滑配置 ---
POSE_CHANGE_THRESHOLD_DEG = 15.0  # 姿态变化阈值(度)，超过此值则进行插值
INTERPOLATION_STEP_DEG = 5.0      # 插值时每一步的最大角度变化(度)

# --- 辅助函数 ---
def normalize_angle_degrees(angle):
    while angle > 180: angle -= 360
    while angle <= -180: angle += 360
    return angle

def parse_gcode_to_segments(filepath):
    # (此函数与v2版本相同，保持不变)
    print(f"📄 正在解析G-code并分段: {filepath}")
    segments = []
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            current_type = None
            for line in f:
                line = line.strip().upper()
                cmd_type = None
                if line.startswith('G0'): cmd_type = 'G0'
                elif line.startswith('G1'): cmd_type = 'G1'
                if cmd_type:
                    coords = dict(coord_regex.findall(line))
                    if all(k in coords for k in ('X', 'Y', 'Z')):
                        point = {'x': float(coords.get('X')),'y': float(coords.get('Y')),'z': float(coords.get('Z')),'a': float(coords.get('A')) if 'A' in coords else None,'b': float(coords.get('B')) if 'B' in coords else None,'c': float(coords.get('C')) if 'C' in coords else None}
                        if not segments or segments[-1]['type'] != cmd_type:
                            segments.append({'type': cmd_type, 'points': [point]})
                        else:
                            segments[-1]['points'].append(point)
        print(f"✅ 解析完成，共找到 {len(segments)} 个运动段。")
        return segments
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}"); return None

def gcode_point_to_pose_deg(point, use_fixed_pose=False):
    """从G-code点计算出目标姿态(度)"""
    if use_fixed_pose:
        return G0_FIXED_POSE_DEG
    
    gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
    gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
    gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C
    
    rx = normalize_angle_degrees(gcode_a + GCODE_TO_ROBOT_OFFSET_A)
    ry = normalize_angle_degrees(gcode_b + GCODE_TO_ROBOT_OFFSET_B)
    rz = normalize_angle_degrees(gcode_c + GCODE_TO_ROBOT_OFFSET_C)
    return [rx, ry, rz]

def create_command_from_pose(position_xyz, pose_deg, velocity, accel, smoothing):
    """从完整位姿数据创建MoveCmd"""
    pose_rad = [math.radians(angle) for angle in pose_deg]
    target_pose = position_xyz + pose_rad
    
    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = nrc.VectorDouble()
    for val in target_pose: cmd.targetPosValue.append(val)
    
    cmd.coord = 3; cmd.userNum = USER_COORD_NUMBER
    cmd.velocity = velocity; cmd.acc = accel; cmd.dec = accel
    cmd.pl = smoothing
    return cmd

def interpolate_points(start_point, end_point, start_pose_deg, end_pose_deg):
    """在两个点之间进行线性和姿态插值"""
    start_pos = np.array([start_point['x'], start_point['y'], start_point['z']])
    end_pos = np.array([end_point['x'], end_point['y'], end_point['z']])
    start_pose = np.array(start_pose_deg)
    end_pose = np.array(end_pose_deg)

    # 处理角度跨越-180/180边界的情况
    for i in range(3):
        diff = end_pose[i] - start_pose[i]
        if diff > 180: end_pose[i] -= 360
        elif diff < -180: end_pose[i] += 360

    pose_change = np.abs(end_pose - start_pose)
    max_angle_change = np.max(pose_change)
    
    num_steps = int(np.ceil(max_angle_change / INTERPOLATION_STEP_DEG))
    if num_steps <= 1: return []

    print(f"\n    ⚠️  检测到姿态突变 > {POSE_CHANGE_THRESHOLD_DEG:.1f}° (最大变化: {max_angle_change:.1f}°)，插入 {num_steps-1} 个中间点...")
    
    interpolated_points = []
    for i in range(1, num_steps):
        t = i / num_steps
        inter_pos = start_pos + t * (end_pos - start_pos)
        inter_pose = start_pose + t * (end_pose - start_pose)
        # 将插值后的角度标准化回 [-180, 180]
        inter_pose_normalized = [normalize_angle_degrees(a) for a in inter_pose]
        interpolated_points.append((list(inter_pos), inter_pose_normalized))
        
    return interpolated_points

# --- 机器人控制与执行函数 ---
def initialize_robot(socket_fd):
    # (此函数与v2版本相同，保持不变)
    print("\n🔧 正在初始化机器人...")
    if not robot_power_on_if_needed(socket_fd): return False
    if nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER) != 0:
        print(f"❌ 设置用户坐标系 {USER_COORD_NUMBER} 失败"); return False
    if nrc.set_current_mode(socket_fd, 1) != 0:
        print("❌ 设置远程模式失败"); return False
    print("✅ 机器人初始化成功。")
    return True

def setup_queue_mode(socket_fd):
    # (此函数与v2版本相同，保持不变)
    print("\n🔄 正在设置队列模式...")
    if nrc.queue_motion_set_status(socket_fd, True) != 0:
        print("❌ 启用队列模式失败"); return False
    if nrc.queue_motion_clear_Data(socket_fd) != 0:
        print("❌ 清空队列数据失败"); return False
    print("✅ 队列模式已启用并清空。")
    return True

def wait_for_batch_complete(socket_fd, timeout_seconds=300):
    # (此函数与v2版本相同，保持不变)
    print("  ⏳ 正在等待批次执行完成...", end="", flush=True)
    start_time = time.time()
    while time.time() - start_time < timeout_seconds:
        try:
            res_state = nrc.get_robot_running_state(socket_fd, 0)
            res_len = nrc.queue_motion_get_queuelen(socket_fd, 0)
            is_stopped = isinstance(res_state, list) and res_state[1] == 0
            is_empty = isinstance(res_len, list) and res_len[1] == 0
            if is_stopped and is_empty:
                print(" ✅"); return True
            time.sleep(0.2); print(".", end="", flush=True)
        except Exception as e:
            print(f"\n⚠️ 检查状态时出错: {e}"); time.sleep(1)
    print(" ❌ 超时!"); return False

def execute_g0_segment(socket_fd, segment):
    # (此函数逻辑稍作调整以返回姿态)
    point = segment['points'][0]
    print(f"  ⚡️ 执行G0快速定位到 X:{point['x']:.2f} Y:{point['y']:.2f} Z:{point['z']:.2f}")
    
    pose_deg = gcode_point_to_pose_deg(point, use_fixed_pose=True)
    cmd = create_command_from_pose([point['x'], point['y'], point['z']], pose_deg, G0_VELOCITY, G0_ACCEL, G0_SMOOTHING)
    
    nrc.queue_motion_clear_Data(socket_fd)
    if nrc.queue_motion_push_back_moveL(socket_fd, cmd) != 0: raise RuntimeError("添加G0指令到队列失败")
    if nrc.queue_motion_send_to_controller(socket_fd, 1) != 0: raise RuntimeError("发送G0指令失败")
    if not wait_for_batch_complete(socket_fd): raise RuntimeError("G0运动执行超时或失败")
    
    return {'point': point, 'pose_deg': pose_deg} # 返回最后一个点的状态

def execute_g1_segment(socket_fd, segment, last_point_state):
    """处理并执行一个G1运动段，带姿态突变检测与插值"""
    points = segment['points']
    if not points: return last_point_state

    print(f"  📏 执行G1直线插补，包含 {len(points)} 个路径点...")
    
    nrc.queue_motion_clear_Data(socket_fd)
    commands_to_send = []
    
    current_point_state = last_point_state

    for point in points:
        # 计算目标姿态
        target_pose_deg = gcode_point_to_pose_deg(point, use_fixed_pose=False)
        
        # 检查姿态变化
        pose_change = np.abs(np.array(target_pose_deg) - np.array(current_point_state['pose_deg']))
        # 同样处理角度跨界
        for i in range(3):
            if pose_change[i] > 180: pose_change[i] = 360 - pose_change[i]
        
        # 如果姿态变化大，则插入中间点
        if np.any(pose_change > POSE_CHANGE_THRESHOLD_DEG):
            interpolated = interpolate_points(current_point_state['point'], point, current_point_state['pose_deg'], target_pose_deg)
            for pos_xyz, pose_deg in interpolated:
                cmd = create_command_from_pose(pos_xyz, pose_deg, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING)
                commands_to_send.append(cmd)

        # 添加最终的目标点
        cmd = create_command_from_pose([point['x'], point['y'], point['z']], target_pose_deg, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING)
        commands_to_send.append(cmd)

        # 更新上一个点的状态
        current_point_state = {'point': point, 'pose_deg': target_pose_deg}

    # 将所有指令添加到队列
    for cmd in commands_to_send:
        if nrc.queue_motion_push_back_moveL(socket_fd, cmd) != 0:
            raise RuntimeError("添加G1指令(MOVL)到队列失败")
            
    # 发送整个批次
    if commands_to_send:
        print(f"  📤 发送 {len(commands_to_send)} 条MOVL指令(包含插值点)到控制器...")
        if nrc.queue_motion_send_to_controller(socket_fd, len(commands_to_send)) != 0:
            raise RuntimeError("发送G1段失败")
        if not wait_for_batch_complete(socket_fd):
            raise RuntimeError("G1段执行超时或失败")

    return current_point_state


# --- 主执行函数 ---
def run_gcode_precision():
    """主执行函数 - 编排整个G-code执行流程。"""
    print("=" * 60); print("INEXBOT机械臂 G-code 路径执行程序 (精密队列模式 - v3)"); print("=" * 60)
    segments = parse_gcode_to_segments(GCODE_FILE)
    if not segments: return
    socket_fd = -1
    try:
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0: raise ConnectionError("连接失败！")
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        if not initialize_robot(socket_fd): raise RuntimeError("机器人初始化失败")
        if not setup_queue_mode(socket_fd): raise RuntimeError("队列模式设置失败")

        print("\n" + "=" * 40); print(f"🚀 开始按段执行 {len(segments)} 个G-code运动段..."); print("=" * 40)
        
        # 初始化上一个点的状态，使用G0的固定姿态
        last_point_state = {'point': {'x':0,'y':0,'z':0}, 'pose_deg': G0_FIXED_POSE_DEG}

        for i, segment in enumerate(segments):
            print(f"\n--- 段 {i+1}/{len(segments)} (类型: {segment['type']}) ---")
            if segment['type'] == 'G0':
                last_point_state = execute_g0_segment(socket_fd, segment)
            elif segment['type'] == 'G1':
                last_point_state = execute_g1_segment(socket_fd, segment, last_point_state)
            print(f"✅ 段 {i+1} 执行完成。")

        print("\n" + "=" * 40); print("🎉 所有G-code路径执行完毕！"); print("=" * 40)
    except (ConnectionError, RuntimeError, Exception) as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        if socket_fd > 0:
            print("\n程序结束，正在安全关闭...")
            nrc.queue_motion_set_status(socket_fd, False)
            robot_power_off(socket_fd)
            print("🔌 正在断开连接..."); nrc.disconnect_robot(socket_fd); print("✅ 连接已断开。")

# --- 依赖的底层函数 ---
def robot_power_on_if_needed(socket_fd):
    try:
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人伺服已上电。"); return True
        print("ℹ️ 机器人需要上电...")
        nrc.clear_error(socket_fd); time.sleep(0.2)
        nrc.set_servo_state(socket_fd, 1); time.sleep(0.2)
        if nrc.set_servo_poweron(socket_fd) != 0:
            print(f"❌ 上电失败！"); return False
        time.sleep(1.5)
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人上电成功！"); return True
        print(f"❌ 上电后状态异常: {res}"); return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}"); return False

def robot_power_off(socket_fd):
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd); time.sleep(1); print("✅ 机器人已下电。")
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")

if __name__ == "__main__":
    run_gcode_precision()