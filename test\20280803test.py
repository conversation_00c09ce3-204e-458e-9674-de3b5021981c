#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (高级队列模式) - v2 修复版

功能:
1.  将G-code文件解析为G0(快速移动)和G1(工作移动)的运动段。
2.  独立处理G0段：使用100%速度和固定安全姿态进行快速定位。
3.  独立处理G1段：使用自定义速度，并通过'moveS'(样条插补)指令实现位置和姿态的平滑过渡。
4.  自动处理G0到G1以及G1内部的姿态突变，确保运动平稳。
5.  所有操作均在队列模式下完成，保证运动的连续性。

v2 修复:
- 修正了“第一条运动指令不能为MOVS”的问题。
- G1段的第一个点现在使用MOVL指令，后续点使用MOVS指令，以正确引导机器人进入样条曲线。
"""

import sys
import os
import time
import re
import math
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
try:
    from config import ROBOT_IP, ROBOT_PORT
    import nrc_interface as nrc
except ImportError:
    print("❌ 错误: 无法导入 nrc_interface 或 config。请确保文件路径正确。")
    sys.exit(1)

# --- 全局参数配置 ---
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1
G0_VELOCITY = 50
G0_ACCEL = 50
G0_SMOOTHING = 0
G0_FIXED_POSE_DEG = [180.0, 0.0, 0.0]
G1_VELOCITY = 10
G1_ACCEL = 10
G1_SMOOTHING = 5
GCODE_DEFAULT_A, GCODE_DEFAULT_B, GCODE_DEFAULT_C = 0.0, 0.0, 0.0
GCODE_TO_ROBOT_OFFSET_A, GCODE_TO_ROBOT_OFFSET_B, GCODE_TO_ROBOT_OFFSET_C = 180.0, 0.0, 0.0

# --- 核心辅助函数 (保持不变) ---
def normalize_angle_degrees(angle):
    while angle > 180: angle -= 360
    while angle <= -180: angle += 360
    return angle

def parse_gcode_to_segments(filepath):
    print(f"📄 正在解析G-code并分段: {filepath}")
    segments = []
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                cmd_type = None
                if line.startswith('G0'): cmd_type = 'G0'
                elif line.startswith('G1'): cmd_type = 'G1'
                
                if cmd_type:
                    coords = dict(coord_regex.findall(line))
                    if all(k in coords for k in ('X', 'Y', 'Z')):
                        point = {
                            'x': float(coords.get('X')), 'y': float(coords.get('Y')), 'z': float(coords.get('Z')),
                            'a': float(coords.get('A')) if 'A' in coords else None,
                            'b': float(coords.get('B')) if 'B' in coords else None,
                            'c': float(coords.get('C')) if 'C' in coords else None
                        }
                        if not segments or segments[-1]['type'] != cmd_type:
                            segments.append({'type': cmd_type, 'points': [point]})
                        else:
                            segments[-1]['points'].append(point)
                            
        print(f"✅ 解析完成，共找到 {len(segments)} 个运动段。")
        return segments
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None

def create_command(point, velocity, accel, smoothing, use_fixed_pose=False):
    if use_fixed_pose:
        rx_deg, ry_deg, rz_deg = G0_FIXED_POSE_DEG
    else:
        gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

        rx_deg = normalize_angle_degrees(gcode_a + GCODE_TO_ROBOT_OFFSET_A)
        ry_deg = normalize_angle_degrees(gcode_b + GCODE_TO_ROBOT_OFFSET_B)
        rz_deg = normalize_angle_degrees(gcode_c + GCODE_TO_ROBOT_OFFSET_C)

    target_pos_rad = [point['x'], point['y'], point['z'], 
                      math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)]

    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = nrc.VectorDouble()
    for val in target_pos_rad:
        cmd.targetPosValue.append(val)
    
    cmd.coord = 3; cmd.userNum = USER_COORD_NUMBER
    cmd.velocity = velocity; cmd.acc = accel; cmd.dec = accel
    cmd.pl = smoothing
    
    return cmd

# --- 机器人控制与执行函数 (execute_g1_segment 已修改) ---

def initialize_robot(socket_fd):
    print("\n🔧 正在初始化机器人...")
    if not robot_power_on_if_needed(socket_fd): return False
    if nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER) != 0:
        print(f"❌ 设置用户坐标系 {USER_COORD_NUMBER} 失败"); return False
    if nrc.set_current_mode(socket_fd, 1) != 0:
        print("❌ 设置远程模式失败"); return False
    print("✅ 机器人初始化成功。")
    return True

def setup_queue_mode(socket_fd):
    print("\n🔄 正在设置队列模式...")
    if nrc.queue_motion_set_status(socket_fd, True) != 0:
        print("❌ 启用队列模式失败"); return False
    if nrc.queue_motion_clear_Data(socket_fd) != 0:
        print("❌ 清空队列数据失败"); return False
    print("✅ 队列模式已启用并清空。")
    return True

def wait_for_batch_complete(socket_fd, timeout_seconds=300):
    print("  ⏳ 正在等待批次执行完成...", end="", flush=True)
    start_time = time.time()
    while time.time() - start_time < timeout_seconds:
        try:
            res_state = nrc.get_robot_running_state(socket_fd, 0)
            res_len = nrc.queue_motion_get_queuelen(socket_fd, 0)
            is_stopped = isinstance(res_state, list) and res_state[1] == 0
            is_empty = isinstance(res_len, list) and res_len[1] == 0
            if is_stopped and is_empty:
                print(" ✅"); return True
            time.sleep(0.2); print(".", end="", flush=True)
        except Exception as e:
            print(f"\n⚠️ 检查状态时出错: {e}"); time.sleep(1)
    print(" ❌ 超时!"); return False

def execute_g0_segment(socket_fd, segment):
    point = segment['points'][0]
    print(f"  ⚡️ 执行G0快速定位到 X:{point['x']:.2f} Y:{point['y']:.2f} Z:{point['z']:.2f}")
    cmd = create_command(point, G0_VELOCITY, G0_ACCEL, G0_SMOOTHING, use_fixed_pose=True)
    nrc.queue_motion_clear_Data(socket_fd)
    if nrc.queue_motion_push_back_moveL(socket_fd, cmd) != 0:
        raise RuntimeError("添加G0指令到队列失败")
    if nrc.queue_motion_send_to_controller(socket_fd, 1) != 0:
        raise RuntimeError("发送G0指令失败")
    if not wait_for_batch_complete(socket_fd):
        raise RuntimeError("G0运动执行超时或失败")

# ---------- v2 修复的核心部分 ----------
def execute_g1_segment(socket_fd, segment):
    """
    处理并执行一个G1运动段。
    第一个点使用MOVL引导，后续点使用MOVS实现平滑插补。
    """
    points = segment['points']
    if not points:
        print("  ⚠️ G1段为空，跳过。")
        return

    print(f"  🌀 执行G1平滑插补，包含 {len(points)} 个路径点...")
    
    nrc.queue_motion_clear_Data(socket_fd)
    commands_added = 0

    # 1. 处理第一个点：使用 MOVL
    first_point = points[0]
    print(f"    - (引导) MOVL to point 1...")
    cmd_movl = create_command(first_point, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING, use_fixed_pose=False)
    if nrc.queue_motion_push_back_moveL(socket_fd, cmd_movl) == 0:
        commands_added += 1
    else:
        raise RuntimeError(f"添加G1引导点(MOVL)到队列失败: {first_point}")

    # 2. 处理后续的点（如果存在）：使用 MOVS
    if len(points) > 1:
        for i, point in enumerate(points[1:]):
            print(f"    - (平滑) MOVS to point {i+2}...")
            cmd_movs = create_command(point, G1_VELOCITY, G1_ACCEL, G1_SMOOTHING, use_fixed_pose=False)
            if nrc.queue_motion_push_back_moveS(socket_fd, cmd_movs) == 0:
                commands_added += 1
            else:
                raise RuntimeError(f"添加G1样条点(MOVS)到队列失败: {point}")
            
    # 3. 发送整个批次
    if commands_added > 0:
        print(f"  📤 发送 {commands_added} 个混合指令(1 MOVL + {commands_added-1} MOVS)到控制器...")
        if nrc.queue_motion_send_to_controller(socket_fd, commands_added) != 0:
            raise RuntimeError("发送G1段失败")
        if not wait_for_batch_complete(socket_fd):
            raise RuntimeError("G1段执行超时或失败")
# ----------------------------------------

def run_gcode_advanced():
    """主执行函数"""
    print("=" * 60); print("INEXBOT机械臂 G-code 路径执行程序 (高级队列模式 - v2 修复版)"); print("=" * 60)
    segments = parse_gcode_to_segments(GCODE_FILE)
    if not segments: return
    socket_fd = -1
    try:
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0: raise ConnectionError("连接失败！")
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        if not initialize_robot(socket_fd): raise RuntimeError("机器人初始化失败")
        if not setup_queue_mode(socket_fd): raise RuntimeError("队列模式设置失败")

        print("\n" + "=" * 40); print(f"🚀 开始按段执行 {len(segments)} 个G-code运动段..."); print("=" * 40)
        
        for i, segment in enumerate(segments):
            print(f"\n--- 段 {i+1}/{len(segments)} (类型: {segment['type']}) ---")
            if segment['type'] == 'G0':
                execute_g0_segment(socket_fd, segment)
            elif segment['type'] == 'G1':
                execute_g1_segment(socket_fd, segment)
            print(f"✅ 段 {i+1} 执行完成。")

        print("\n" + "=" * 40); print("🎉 所有G-code路径执行完毕！"); print("=" * 40)
    except (ConnectionError, RuntimeError, Exception) as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        if socket_fd > 0:
            print("\n程序结束，正在安全关闭...")
            nrc.queue_motion_set_status(socket_fd, False)
            robot_power_off(socket_fd)
            print("🔌 正在断开连接..."); nrc.disconnect_robot(socket_fd); print("✅ 连接已断开。")

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人伺服已上电。"); return True
        print("ℹ️ 机器人需要上电...")
        nrc.clear_error(socket_fd); time.sleep(0.2)
        nrc.set_servo_state(socket_fd, 1); time.sleep(0.2)
        if nrc.set_servo_poweron(socket_fd) != 0:
            print(f"❌ 上电失败！"); return False
        time.sleep(1.5)
        res = nrc.get_servo_state(socket_fd, 0)
        if isinstance(res, list) and len(res) > 1 and res[1] == 3:
            print("✅ 机器人上电成功！"); return True
        print(f"❌ 上电后状态异常: {res}"); return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}"); return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd); time.sleep(1); print("✅ 机器人已下电。")
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")

if __name__ == "__main__":
    run_gcode_advanced()