# This file was automatically generated by SWIG (https://www.swig.org).
# Version 4.2.1
#
# Do not make changes to this file unless you know what you are doing - modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info
# Import the low-level C/C++ module
if __package__ or "." in __name__:
    from . import nrc_host
else:
    import nrc_host

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__

def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "this":
            set(self, name, value)
        elif name == "thisown":
            self.this.own(value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)
    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)
    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""
    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())
    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""
    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


class SwigPyIterator(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = nrc_host.delete_SwigPyIterator

    def value(self):
        return nrc_host.SwigPyIterator_value(self)

    def incr(self, n=1):
        return nrc_host.SwigPyIterator_incr(self, n)

    def decr(self, n=1):
        return nrc_host.SwigPyIterator_decr(self, n)

    def distance(self, x):
        return nrc_host.SwigPyIterator_distance(self, x)

    def equal(self, x):
        return nrc_host.SwigPyIterator_equal(self, x)

    def copy(self):
        return nrc_host.SwigPyIterator_copy(self)

    def next(self):
        return nrc_host.SwigPyIterator_next(self)

    def __next__(self):
        return nrc_host.SwigPyIterator___next__(self)

    def previous(self):
        return nrc_host.SwigPyIterator_previous(self)

    def advance(self, n):
        return nrc_host.SwigPyIterator_advance(self, n)

    def __eq__(self, x):
        return nrc_host.SwigPyIterator___eq__(self, x)

    def __ne__(self, x):
        return nrc_host.SwigPyIterator___ne__(self, x)

    def __iadd__(self, n):
        return nrc_host.SwigPyIterator___iadd__(self, n)

    def __isub__(self, n):
        return nrc_host.SwigPyIterator___isub__(self, n)

    def __add__(self, n):
        return nrc_host.SwigPyIterator___add__(self, n)

    def __sub__(self, *args):
        return nrc_host.SwigPyIterator___sub__(self, *args)
    def __iter__(self):
        return self

# Register SwigPyIterator in nrc_host:
nrc_host.SwigPyIterator_swigregister(SwigPyIterator)
class VectorChar(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorChar_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorChar___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorChar___bool__(self)

    def __len__(self):
        return nrc_host.VectorChar___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorChar___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorChar___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorChar___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorChar___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorChar___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorChar___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorChar_pop(self)

    def append(self, x):
        return nrc_host.VectorChar_append(self, x)

    def empty(self):
        return nrc_host.VectorChar_empty(self)

    def size(self):
        return nrc_host.VectorChar_size(self)

    def swap(self, v):
        return nrc_host.VectorChar_swap(self, v)

    def begin(self):
        return nrc_host.VectorChar_begin(self)

    def end(self):
        return nrc_host.VectorChar_end(self)

    def rbegin(self):
        return nrc_host.VectorChar_rbegin(self)

    def rend(self):
        return nrc_host.VectorChar_rend(self)

    def clear(self):
        return nrc_host.VectorChar_clear(self)

    def get_allocator(self):
        return nrc_host.VectorChar_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorChar_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorChar_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorChar_swiginit(self, nrc_host.new_VectorChar(*args))

    def push_back(self, x):
        return nrc_host.VectorChar_push_back(self, x)

    def front(self):
        return nrc_host.VectorChar_front(self)

    def back(self):
        return nrc_host.VectorChar_back(self)

    def assign(self, n, x):
        return nrc_host.VectorChar_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorChar_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorChar_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorChar_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorChar_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorChar

# Register VectorChar in nrc_host:
nrc_host.VectorChar_swigregister(VectorChar)
class VectorInt(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorInt_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorInt___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorInt___bool__(self)

    def __len__(self):
        return nrc_host.VectorInt___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorInt___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorInt___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorInt___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorInt___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorInt___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorInt___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorInt_pop(self)

    def append(self, x):
        return nrc_host.VectorInt_append(self, x)

    def empty(self):
        return nrc_host.VectorInt_empty(self)

    def size(self):
        return nrc_host.VectorInt_size(self)

    def swap(self, v):
        return nrc_host.VectorInt_swap(self, v)

    def begin(self):
        return nrc_host.VectorInt_begin(self)

    def end(self):
        return nrc_host.VectorInt_end(self)

    def rbegin(self):
        return nrc_host.VectorInt_rbegin(self)

    def rend(self):
        return nrc_host.VectorInt_rend(self)

    def clear(self):
        return nrc_host.VectorInt_clear(self)

    def get_allocator(self):
        return nrc_host.VectorInt_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorInt_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorInt_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorInt_swiginit(self, nrc_host.new_VectorInt(*args))

    def push_back(self, x):
        return nrc_host.VectorInt_push_back(self, x)

    def front(self):
        return nrc_host.VectorInt_front(self)

    def back(self):
        return nrc_host.VectorInt_back(self)

    def assign(self, n, x):
        return nrc_host.VectorInt_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorInt_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorInt_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorInt_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorInt_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorInt

# Register VectorInt in nrc_host:
nrc_host.VectorInt_swigregister(VectorInt)
class VectorDouble(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorDouble_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorDouble___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorDouble___bool__(self)

    def __len__(self):
        return nrc_host.VectorDouble___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorDouble___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorDouble___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorDouble___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorDouble___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorDouble___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorDouble___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorDouble_pop(self)

    def append(self, x):
        return nrc_host.VectorDouble_append(self, x)

    def empty(self):
        return nrc_host.VectorDouble_empty(self)

    def size(self):
        return nrc_host.VectorDouble_size(self)

    def swap(self, v):
        return nrc_host.VectorDouble_swap(self, v)

    def begin(self):
        return nrc_host.VectorDouble_begin(self)

    def end(self):
        return nrc_host.VectorDouble_end(self)

    def rbegin(self):
        return nrc_host.VectorDouble_rbegin(self)

    def rend(self):
        return nrc_host.VectorDouble_rend(self)

    def clear(self):
        return nrc_host.VectorDouble_clear(self)

    def get_allocator(self):
        return nrc_host.VectorDouble_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorDouble_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorDouble_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorDouble_swiginit(self, nrc_host.new_VectorDouble(*args))

    def push_back(self, x):
        return nrc_host.VectorDouble_push_back(self, x)

    def front(self):
        return nrc_host.VectorDouble_front(self)

    def back(self):
        return nrc_host.VectorDouble_back(self)

    def assign(self, n, x):
        return nrc_host.VectorDouble_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorDouble_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorDouble_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorDouble_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorDouble_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorDouble

# Register VectorDouble in nrc_host:
nrc_host.VectorDouble_swigregister(VectorDouble)
class VectorString(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorString_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorString___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorString___bool__(self)

    def __len__(self):
        return nrc_host.VectorString___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorString___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorString___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorString___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorString___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorString___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorString___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorString_pop(self)

    def append(self, x):
        return nrc_host.VectorString_append(self, x)

    def empty(self):
        return nrc_host.VectorString_empty(self)

    def size(self):
        return nrc_host.VectorString_size(self)

    def swap(self, v):
        return nrc_host.VectorString_swap(self, v)

    def begin(self):
        return nrc_host.VectorString_begin(self)

    def end(self):
        return nrc_host.VectorString_end(self)

    def rbegin(self):
        return nrc_host.VectorString_rbegin(self)

    def rend(self):
        return nrc_host.VectorString_rend(self)

    def clear(self):
        return nrc_host.VectorString_clear(self)

    def get_allocator(self):
        return nrc_host.VectorString_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorString_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorString_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorString_swiginit(self, nrc_host.new_VectorString(*args))

    def push_back(self, x):
        return nrc_host.VectorString_push_back(self, x)

    def front(self):
        return nrc_host.VectorString_front(self)

    def back(self):
        return nrc_host.VectorString_back(self)

    def assign(self, n, x):
        return nrc_host.VectorString_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorString_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorString_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorString_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorString_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorString

# Register VectorString in nrc_host:
nrc_host.VectorString_swigregister(VectorString)
class VectorCondition(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorCondition_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorCondition___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorCondition___bool__(self)

    def __len__(self):
        return nrc_host.VectorCondition___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorCondition___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorCondition___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorCondition___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorCondition___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorCondition___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorCondition___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorCondition_pop(self)

    def append(self, x):
        return nrc_host.VectorCondition_append(self, x)

    def empty(self):
        return nrc_host.VectorCondition_empty(self)

    def size(self):
        return nrc_host.VectorCondition_size(self)

    def swap(self, v):
        return nrc_host.VectorCondition_swap(self, v)

    def begin(self):
        return nrc_host.VectorCondition_begin(self)

    def end(self):
        return nrc_host.VectorCondition_end(self)

    def rbegin(self):
        return nrc_host.VectorCondition_rbegin(self)

    def rend(self):
        return nrc_host.VectorCondition_rend(self)

    def clear(self):
        return nrc_host.VectorCondition_clear(self)

    def get_allocator(self):
        return nrc_host.VectorCondition_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorCondition_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorCondition_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorCondition_swiginit(self, nrc_host.new_VectorCondition(*args))

    def push_back(self, x):
        return nrc_host.VectorCondition_push_back(self, x)

    def front(self):
        return nrc_host.VectorCondition_front(self)

    def back(self):
        return nrc_host.VectorCondition_back(self)

    def assign(self, n, x):
        return nrc_host.VectorCondition_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorCondition_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorCondition_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorCondition_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorCondition_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorCondition

# Register VectorCondition in nrc_host:
nrc_host.VectorCondition_swigregister(VectorCondition)
class VectorVectorCondition(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorVectorCondition_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorVectorCondition___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorVectorCondition___bool__(self)

    def __len__(self):
        return nrc_host.VectorVectorCondition___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorVectorCondition___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorVectorCondition___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorVectorCondition___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorVectorCondition___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorVectorCondition___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorVectorCondition___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorVectorCondition_pop(self)

    def append(self, x):
        return nrc_host.VectorVectorCondition_append(self, x)

    def empty(self):
        return nrc_host.VectorVectorCondition_empty(self)

    def size(self):
        return nrc_host.VectorVectorCondition_size(self)

    def swap(self, v):
        return nrc_host.VectorVectorCondition_swap(self, v)

    def begin(self):
        return nrc_host.VectorVectorCondition_begin(self)

    def end(self):
        return nrc_host.VectorVectorCondition_end(self)

    def rbegin(self):
        return nrc_host.VectorVectorCondition_rbegin(self)

    def rend(self):
        return nrc_host.VectorVectorCondition_rend(self)

    def clear(self):
        return nrc_host.VectorVectorCondition_clear(self)

    def get_allocator(self):
        return nrc_host.VectorVectorCondition_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorVectorCondition_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorVectorCondition_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorVectorCondition_swiginit(self, nrc_host.new_VectorVectorCondition(*args))

    def push_back(self, x):
        return nrc_host.VectorVectorCondition_push_back(self, x)

    def front(self):
        return nrc_host.VectorVectorCondition_front(self)

    def back(self):
        return nrc_host.VectorVectorCondition_back(self)

    def assign(self, n, x):
        return nrc_host.VectorVectorCondition_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorVectorCondition_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorVectorCondition_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorVectorCondition_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorVectorCondition_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorVectorCondition

# Register VectorVectorCondition in nrc_host:
nrc_host.VectorVectorCondition_swigregister(VectorVectorCondition)
class VectorVectorInt(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorVectorInt_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorVectorInt___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorVectorInt___bool__(self)

    def __len__(self):
        return nrc_host.VectorVectorInt___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorVectorInt___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorVectorInt___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorVectorInt___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorVectorInt___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorVectorInt___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorVectorInt___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorVectorInt_pop(self)

    def append(self, x):
        return nrc_host.VectorVectorInt_append(self, x)

    def empty(self):
        return nrc_host.VectorVectorInt_empty(self)

    def size(self):
        return nrc_host.VectorVectorInt_size(self)

    def swap(self, v):
        return nrc_host.VectorVectorInt_swap(self, v)

    def begin(self):
        return nrc_host.VectorVectorInt_begin(self)

    def end(self):
        return nrc_host.VectorVectorInt_end(self)

    def rbegin(self):
        return nrc_host.VectorVectorInt_rbegin(self)

    def rend(self):
        return nrc_host.VectorVectorInt_rend(self)

    def clear(self):
        return nrc_host.VectorVectorInt_clear(self)

    def get_allocator(self):
        return nrc_host.VectorVectorInt_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorVectorInt_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorVectorInt_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorVectorInt_swiginit(self, nrc_host.new_VectorVectorInt(*args))

    def push_back(self, x):
        return nrc_host.VectorVectorInt_push_back(self, x)

    def front(self):
        return nrc_host.VectorVectorInt_front(self)

    def back(self):
        return nrc_host.VectorVectorInt_back(self)

    def assign(self, n, x):
        return nrc_host.VectorVectorInt_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorVectorInt_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorVectorInt_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorVectorInt_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorVectorInt_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorVectorInt

# Register VectorVectorInt in nrc_host:
nrc_host.VectorVectorInt_swigregister(VectorVectorInt)
class VectorAlarmdIO(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorAlarmdIO_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorAlarmdIO___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorAlarmdIO___bool__(self)

    def __len__(self):
        return nrc_host.VectorAlarmdIO___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorAlarmdIO___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorAlarmdIO___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorAlarmdIO___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorAlarmdIO___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorAlarmdIO___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorAlarmdIO___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorAlarmdIO_pop(self)

    def append(self, x):
        return nrc_host.VectorAlarmdIO_append(self, x)

    def empty(self):
        return nrc_host.VectorAlarmdIO_empty(self)

    def size(self):
        return nrc_host.VectorAlarmdIO_size(self)

    def swap(self, v):
        return nrc_host.VectorAlarmdIO_swap(self, v)

    def begin(self):
        return nrc_host.VectorAlarmdIO_begin(self)

    def end(self):
        return nrc_host.VectorAlarmdIO_end(self)

    def rbegin(self):
        return nrc_host.VectorAlarmdIO_rbegin(self)

    def rend(self):
        return nrc_host.VectorAlarmdIO_rend(self)

    def clear(self):
        return nrc_host.VectorAlarmdIO_clear(self)

    def get_allocator(self):
        return nrc_host.VectorAlarmdIO_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorAlarmdIO_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorAlarmdIO_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorAlarmdIO_swiginit(self, nrc_host.new_VectorAlarmdIO(*args))

    def push_back(self, x):
        return nrc_host.VectorAlarmdIO_push_back(self, x)

    def front(self):
        return nrc_host.VectorAlarmdIO_front(self)

    def back(self):
        return nrc_host.VectorAlarmdIO_back(self)

    def assign(self, n, x):
        return nrc_host.VectorAlarmdIO_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorAlarmdIO_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorAlarmdIO_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorAlarmdIO_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorAlarmdIO_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorAlarmdIO

# Register VectorAlarmdIO in nrc_host:
nrc_host.VectorAlarmdIO_swigregister(VectorAlarmdIO)
class VectorRemoteProgram(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorRemoteProgram_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorRemoteProgram___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorRemoteProgram___bool__(self)

    def __len__(self):
        return nrc_host.VectorRemoteProgram___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorRemoteProgram___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorRemoteProgram___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorRemoteProgram___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorRemoteProgram___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorRemoteProgram___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorRemoteProgram___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorRemoteProgram_pop(self)

    def append(self, x):
        return nrc_host.VectorRemoteProgram_append(self, x)

    def empty(self):
        return nrc_host.VectorRemoteProgram_empty(self)

    def size(self):
        return nrc_host.VectorRemoteProgram_size(self)

    def swap(self, v):
        return nrc_host.VectorRemoteProgram_swap(self, v)

    def begin(self):
        return nrc_host.VectorRemoteProgram_begin(self)

    def end(self):
        return nrc_host.VectorRemoteProgram_end(self)

    def rbegin(self):
        return nrc_host.VectorRemoteProgram_rbegin(self)

    def rend(self):
        return nrc_host.VectorRemoteProgram_rend(self)

    def clear(self):
        return nrc_host.VectorRemoteProgram_clear(self)

    def get_allocator(self):
        return nrc_host.VectorRemoteProgram_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorRemoteProgram_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorRemoteProgram_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorRemoteProgram_swiginit(self, nrc_host.new_VectorRemoteProgram(*args))

    def push_back(self, x):
        return nrc_host.VectorRemoteProgram_push_back(self, x)

    def front(self):
        return nrc_host.VectorRemoteProgram_front(self)

    def back(self):
        return nrc_host.VectorRemoteProgram_back(self)

    def assign(self, n, x):
        return nrc_host.VectorRemoteProgram_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorRemoteProgram_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorRemoteProgram_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorRemoteProgram_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorRemoteProgram_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorRemoteProgram

# Register VectorRemoteProgram in nrc_host:
nrc_host.VectorRemoteProgram_swigregister(VectorRemoteProgram)
class VectorRemoteProgramSetting(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorRemoteProgramSetting_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorRemoteProgramSetting___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorRemoteProgramSetting___bool__(self)

    def __len__(self):
        return nrc_host.VectorRemoteProgramSetting___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorRemoteProgramSetting___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorRemoteProgramSetting___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorRemoteProgramSetting___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorRemoteProgramSetting___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorRemoteProgramSetting___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorRemoteProgramSetting___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorRemoteProgramSetting_pop(self)

    def append(self, x):
        return nrc_host.VectorRemoteProgramSetting_append(self, x)

    def empty(self):
        return nrc_host.VectorRemoteProgramSetting_empty(self)

    def size(self):
        return nrc_host.VectorRemoteProgramSetting_size(self)

    def swap(self, v):
        return nrc_host.VectorRemoteProgramSetting_swap(self, v)

    def begin(self):
        return nrc_host.VectorRemoteProgramSetting_begin(self)

    def end(self):
        return nrc_host.VectorRemoteProgramSetting_end(self)

    def rbegin(self):
        return nrc_host.VectorRemoteProgramSetting_rbegin(self)

    def rend(self):
        return nrc_host.VectorRemoteProgramSetting_rend(self)

    def clear(self):
        return nrc_host.VectorRemoteProgramSetting_clear(self)

    def get_allocator(self):
        return nrc_host.VectorRemoteProgramSetting_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorRemoteProgramSetting_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorRemoteProgramSetting_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorRemoteProgramSetting_swiginit(self, nrc_host.new_VectorRemoteProgramSetting(*args))

    def push_back(self, x):
        return nrc_host.VectorRemoteProgramSetting_push_back(self, x)

    def front(self):
        return nrc_host.VectorRemoteProgramSetting_front(self)

    def back(self):
        return nrc_host.VectorRemoteProgramSetting_back(self)

    def assign(self, n, x):
        return nrc_host.VectorRemoteProgramSetting_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorRemoteProgramSetting_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorRemoteProgramSetting_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorRemoteProgramSetting_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorRemoteProgramSetting_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorRemoteProgramSetting

# Register VectorRemoteProgramSetting in nrc_host:
nrc_host.VectorRemoteProgramSetting_swigregister(VectorRemoteProgramSetting)
class VectorVectorDouble(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return nrc_host.VectorVectorDouble_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return nrc_host.VectorVectorDouble___nonzero__(self)

    def __bool__(self):
        return nrc_host.VectorVectorDouble___bool__(self)

    def __len__(self):
        return nrc_host.VectorVectorDouble___len__(self)

    def __getslice__(self, i, j):
        return nrc_host.VectorVectorDouble___getslice__(self, i, j)

    def __setslice__(self, *args):
        return nrc_host.VectorVectorDouble___setslice__(self, *args)

    def __delslice__(self, i, j):
        return nrc_host.VectorVectorDouble___delslice__(self, i, j)

    def __delitem__(self, *args):
        return nrc_host.VectorVectorDouble___delitem__(self, *args)

    def __getitem__(self, *args):
        return nrc_host.VectorVectorDouble___getitem__(self, *args)

    def __setitem__(self, *args):
        return nrc_host.VectorVectorDouble___setitem__(self, *args)

    def pop(self):
        return nrc_host.VectorVectorDouble_pop(self)

    def append(self, x):
        return nrc_host.VectorVectorDouble_append(self, x)

    def empty(self):
        return nrc_host.VectorVectorDouble_empty(self)

    def size(self):
        return nrc_host.VectorVectorDouble_size(self)

    def swap(self, v):
        return nrc_host.VectorVectorDouble_swap(self, v)

    def begin(self):
        return nrc_host.VectorVectorDouble_begin(self)

    def end(self):
        return nrc_host.VectorVectorDouble_end(self)

    def rbegin(self):
        return nrc_host.VectorVectorDouble_rbegin(self)

    def rend(self):
        return nrc_host.VectorVectorDouble_rend(self)

    def clear(self):
        return nrc_host.VectorVectorDouble_clear(self)

    def get_allocator(self):
        return nrc_host.VectorVectorDouble_get_allocator(self)

    def pop_back(self):
        return nrc_host.VectorVectorDouble_pop_back(self)

    def erase(self, *args):
        return nrc_host.VectorVectorDouble_erase(self, *args)

    def __init__(self, *args):
        nrc_host.VectorVectorDouble_swiginit(self, nrc_host.new_VectorVectorDouble(*args))

    def push_back(self, x):
        return nrc_host.VectorVectorDouble_push_back(self, x)

    def front(self):
        return nrc_host.VectorVectorDouble_front(self)

    def back(self):
        return nrc_host.VectorVectorDouble_back(self)

    def assign(self, n, x):
        return nrc_host.VectorVectorDouble_assign(self, n, x)

    def resize(self, *args):
        return nrc_host.VectorVectorDouble_resize(self, *args)

    def insert(self, *args):
        return nrc_host.VectorVectorDouble_insert(self, *args)

    def reserve(self, n):
        return nrc_host.VectorVectorDouble_reserve(self, n)

    def capacity(self):
        return nrc_host.VectorVectorDouble_capacity(self)
    __swig_destroy__ = nrc_host.delete_VectorVectorDouble

# Register VectorVectorDouble in nrc_host:
nrc_host.VectorVectorDouble_swigregister(VectorVectorDouble)
class ConveyorBasicParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    conveyorID = property(nrc_host.ConveyorBasicParams_conveyorID_get, nrc_host.ConveyorBasicParams_conveyorID_set)
    compensation_encoderVal = property(nrc_host.ConveyorBasicParams_compensation_encoderVal_get, nrc_host.ConveyorBasicParams_compensation_encoderVal_set)
    compensation_time = property(nrc_host.ConveyorBasicParams_compensation_time_get, nrc_host.ConveyorBasicParams_compensation_time_set)
    conveyor_encoderDirection = property(nrc_host.ConveyorBasicParams_conveyor_encoderDirection_get, nrc_host.ConveyorBasicParams_conveyor_encoderDirection_set)
    conveyor_encoderResolution = property(nrc_host.ConveyorBasicParams_conveyor_encoderResolution_get, nrc_host.ConveyorBasicParams_conveyor_encoderResolution_set)
    conveyor_encoderValue = property(nrc_host.ConveyorBasicParams_conveyor_encoderValue_get, nrc_host.ConveyorBasicParams_conveyor_encoderValue_set)
    conveyor_maxEncoderVal = property(nrc_host.ConveyorBasicParams_conveyor_maxEncoderVal_get, nrc_host.ConveyorBasicParams_conveyor_maxEncoderVal_set)
    conveyor_minEncoderVal = property(nrc_host.ConveyorBasicParams_conveyor_minEncoderVal_get, nrc_host.ConveyorBasicParams_conveyor_minEncoderVal_set)
    conveyor_posRecordMode = property(nrc_host.ConveyorBasicParams_conveyor_posRecordMode_get, nrc_host.ConveyorBasicParams_conveyor_posRecordMode_set)
    conveyor_speed = property(nrc_host.ConveyorBasicParams_conveyor_speed_get, nrc_host.ConveyorBasicParams_conveyor_speed_set)
    conveyor_userCoord = property(nrc_host.ConveyorBasicParams_conveyor_userCoord_get, nrc_host.ConveyorBasicParams_conveyor_userCoord_set)
    track_height = property(nrc_host.ConveyorBasicParams_track_height_get, nrc_host.ConveyorBasicParams_track_height_set)
    track_on_run_mode_with_target_overrun = property(nrc_host.ConveyorBasicParams_track_on_run_mode_with_target_overrun_get, nrc_host.ConveyorBasicParams_track_on_run_mode_with_target_overrun_set)

    def __init__(self):
        nrc_host.ConveyorBasicParams_swiginit(self, nrc_host.new_ConveyorBasicParams())
    __swig_destroy__ = nrc_host.delete_ConveyorBasicParams

# Register ConveyorBasicParams in nrc_host:
nrc_host.ConveyorBasicParams_swigregister(ConveyorBasicParams)
class ConveyorIdentificationParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    conveyorID = property(nrc_host.ConveyorIdentificationParams_conveyorID_get, nrc_host.ConveyorIdentificationParams_conveyorID_set)
    detectSrc_DI_capturePos = property(nrc_host.ConveyorIdentificationParams_detectSrc_DI_capturePos_get, nrc_host.ConveyorIdentificationParams_detectSrc_DI_capturePos_set)
    detectSrc_globalVar = property(nrc_host.ConveyorIdentificationParams_detectSrc_globalVar_get, nrc_host.ConveyorIdentificationParams_detectSrc_globalVar_set)
    detectSrc_type = property(nrc_host.ConveyorIdentificationParams_detectSrc_type_get, nrc_host.ConveyorIdentificationParams_detectSrc_type_set)
    detectSrc_visionID = property(nrc_host.ConveyorIdentificationParams_detectSrc_visionID_get, nrc_host.ConveyorIdentificationParams_detectSrc_visionID_set)
    detectSrc_vision_io_filter_type = property(nrc_host.ConveyorIdentificationParams_detectSrc_vision_io_filter_type_get, nrc_host.ConveyorIdentificationParams_detectSrc_vision_io_filter_type_set)
    detectSrc_vision_latch_encoder_value_type = property(nrc_host.ConveyorIdentificationParams_detectSrc_vision_latch_encoder_value_type_get, nrc_host.ConveyorIdentificationParams_detectSrc_vision_latch_encoder_value_type_set)
    identification_communication = property(nrc_host.ConveyorIdentificationParams_identification_communication_get, nrc_host.ConveyorIdentificationParams_identification_communication_set)
    identification_sensorTrg = property(nrc_host.ConveyorIdentificationParams_identification_sensorTrg_get, nrc_host.ConveyorIdentificationParams_identification_sensorTrg_set)
    identification_type = property(nrc_host.ConveyorIdentificationParams_identification_type_get, nrc_host.ConveyorIdentificationParams_identification_type_set)

    def __init__(self):
        nrc_host.ConveyorIdentificationParams_swiginit(self, nrc_host.new_ConveyorIdentificationParams())
    __swig_destroy__ = nrc_host.delete_ConveyorIdentificationParams

# Register ConveyorIdentificationParams in nrc_host:
nrc_host.ConveyorIdentificationParams_swigregister(ConveyorIdentificationParams)
class ConveyorSensorParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    conveyorID = property(nrc_host.ConveyorSensorParams_conveyorID_get, nrc_host.ConveyorSensorParams_conveyorID_set)
    sensorPosDeg_A = property(nrc_host.ConveyorSensorParams_sensorPosDeg_A_get, nrc_host.ConveyorSensorParams_sensorPosDeg_A_set)
    sensorPosDeg_B = property(nrc_host.ConveyorSensorParams_sensorPosDeg_B_get, nrc_host.ConveyorSensorParams_sensorPosDeg_B_set)
    sensorPosDeg_C = property(nrc_host.ConveyorSensorParams_sensorPosDeg_C_get, nrc_host.ConveyorSensorParams_sensorPosDeg_C_set)
    sensorPosDeg_X = property(nrc_host.ConveyorSensorParams_sensorPosDeg_X_get, nrc_host.ConveyorSensorParams_sensorPosDeg_X_set)
    sensorPosDeg_Y = property(nrc_host.ConveyorSensorParams_sensorPosDeg_Y_get, nrc_host.ConveyorSensorParams_sensorPosDeg_Y_set)
    sensorPosDeg_Z = property(nrc_host.ConveyorSensorParams_sensorPosDeg_Z_get, nrc_host.ConveyorSensorParams_sensorPosDeg_Z_set)

    def __init__(self):
        nrc_host.ConveyorSensorParams_swiginit(self, nrc_host.new_ConveyorSensorParams())
    __swig_destroy__ = nrc_host.delete_ConveyorSensorParams

# Register ConveyorSensorParams in nrc_host:
nrc_host.ConveyorSensorParams_swigregister(ConveyorSensorParams)
class ConveyorTrackRangeParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    conveyorID = property(nrc_host.ConveyorTrackRangeParams_conveyorID_get, nrc_host.ConveyorTrackRangeParams_conveyorID_set)
    position_receLatestPos = property(nrc_host.ConveyorTrackRangeParams_position_receLatestPos_get, nrc_host.ConveyorTrackRangeParams_position_receLatestPos_set)
    position_trackRangeXMax = property(nrc_host.ConveyorTrackRangeParams_position_trackRangeXMax_get, nrc_host.ConveyorTrackRangeParams_position_trackRangeXMax_set)
    position_trackRangeYMax = property(nrc_host.ConveyorTrackRangeParams_position_trackRangeYMax_get, nrc_host.ConveyorTrackRangeParams_position_trackRangeYMax_set)
    position_trackRangeYMin = property(nrc_host.ConveyorTrackRangeParams_position_trackRangeYMin_get, nrc_host.ConveyorTrackRangeParams_position_trackRangeYMin_set)
    position_trackRangeZMax = property(nrc_host.ConveyorTrackRangeParams_position_trackRangeZMax_get, nrc_host.ConveyorTrackRangeParams_position_trackRangeZMax_set)
    position_trackRangeZMin = property(nrc_host.ConveyorTrackRangeParams_position_trackRangeZMin_get, nrc_host.ConveyorTrackRangeParams_position_trackRangeZMin_set)
    position_trackStartXPoint = property(nrc_host.ConveyorTrackRangeParams_position_trackStartXPoint_get, nrc_host.ConveyorTrackRangeParams_position_trackStartXPoint_set)

    def __init__(self):
        nrc_host.ConveyorTrackRangeParams_swiginit(self, nrc_host.new_ConveyorTrackRangeParams())
    __swig_destroy__ = nrc_host.delete_ConveyorTrackRangeParams

# Register ConveyorTrackRangeParams in nrc_host:
nrc_host.ConveyorTrackRangeParams_swigregister(ConveyorTrackRangeParams)
class ConveyorWaitPointParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    conveyorID = property(nrc_host.ConveyorWaitPointParams_conveyorID_get, nrc_host.ConveyorWaitPointParams_conveyorID_set)
    delayDetectTime = property(nrc_host.ConveyorWaitPointParams_delayDetectTime_get, nrc_host.ConveyorWaitPointParams_delayDetectTime_set)
    isWait = property(nrc_host.ConveyorWaitPointParams_isWait_get, nrc_host.ConveyorWaitPointParams_isWait_set)
    pos = property(nrc_host.ConveyorWaitPointParams_pos_get, nrc_host.ConveyorWaitPointParams_pos_set)

    def __init__(self):
        nrc_host.ConveyorWaitPointParams_swiginit(self, nrc_host.new_ConveyorWaitPointParams())
    __swig_destroy__ = nrc_host.delete_ConveyorWaitPointParams

# Register ConveyorWaitPointParams in nrc_host:
nrc_host.ConveyorWaitPointParams_swigregister(ConveyorWaitPointParams)
class LaserCuttingEquipment(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    RetreatDistance = property(nrc_host.LaserCuttingEquipment_RetreatDistance_get, nrc_host.LaserCuttingEquipment_RetreatDistance_set)
    arrivalOutLightMode = property(nrc_host.LaserCuttingEquipment_arrivalOutLightMode_get, nrc_host.LaserCuttingEquipment_arrivalOutLightMode_set)
    collisionDistance = property(nrc_host.LaserCuttingEquipment_collisionDistance_get, nrc_host.LaserCuttingEquipment_collisionDistance_set)
    delAspiratedMode = property(nrc_host.LaserCuttingEquipment_delAspiratedMode_get, nrc_host.LaserCuttingEquipment_delAspiratedMode_set)
    delAspiratedTime = property(nrc_host.LaserCuttingEquipment_delAspiratedTime_get, nrc_host.LaserCuttingEquipment_delAspiratedTime_set)
    focusCompensation = property(nrc_host.LaserCuttingEquipment_focusCompensation_get, nrc_host.LaserCuttingEquipment_focusCompensation_set)
    focusCompensationConstant = property(nrc_host.LaserCuttingEquipment_focusCompensationConstant_get, nrc_host.LaserCuttingEquipment_focusCompensationConstant_set)
    focusCompensationPower = property(nrc_host.LaserCuttingEquipment_focusCompensationPower_get, nrc_host.LaserCuttingEquipment_focusCompensationPower_set)
    focusCompensationTime = property(nrc_host.LaserCuttingEquipment_focusCompensationTime_get, nrc_host.LaserCuttingEquipment_focusCompensationTime_set)
    focusFormula = property(nrc_host.LaserCuttingEquipment_focusFormula_get, nrc_host.LaserCuttingEquipment_focusFormula_set)
    follow = property(nrc_host.LaserCuttingEquipment_follow_get, nrc_host.LaserCuttingEquipment_follow_set)
    preAspiratedTime = property(nrc_host.LaserCuttingEquipment_preAspiratedTime_get, nrc_host.LaserCuttingEquipment_preAspiratedTime_set)
    rePerforate = property(nrc_host.LaserCuttingEquipment_rePerforate_get, nrc_host.LaserCuttingEquipment_rePerforate_set)
    waitFollowTime = property(nrc_host.LaserCuttingEquipment_waitFollowTime_get, nrc_host.LaserCuttingEquipment_waitFollowTime_set)
    waitLiftUpTime = property(nrc_host.LaserCuttingEquipment_waitLiftUpTime_get, nrc_host.LaserCuttingEquipment_waitLiftUpTime_set)

    def __init__(self):
        nrc_host.LaserCuttingEquipment_swiginit(self, nrc_host.new_LaserCuttingEquipment())
    __swig_destroy__ = nrc_host.delete_LaserCuttingEquipment

# Register LaserCuttingEquipment in nrc_host:
nrc_host.LaserCuttingEquipment_swigregister(LaserCuttingEquipment)
class LaserCuttingGlobalParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    equipment = property(nrc_host.LaserCuttingGlobalParam_equipment_get, nrc_host.LaserCuttingGlobalParam_equipment_set)

    def __init__(self):
        nrc_host.LaserCuttingGlobalParam_swiginit(self, nrc_host.new_LaserCuttingGlobalParam())
    __swig_destroy__ = nrc_host.delete_LaserCuttingGlobalParam

# Register LaserCuttingGlobalParam in nrc_host:
nrc_host.LaserCuttingGlobalParam_swigregister(LaserCuttingGlobalParam)
class LaserCuttingParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    dutyRatio = property(nrc_host.LaserCuttingParam_dutyRatio_get, nrc_host.LaserCuttingParam_dutyRatio_set)
    focusPosition = property(nrc_host.LaserCuttingParam_focusPosition_get, nrc_host.LaserCuttingParam_focusPosition_set)
    followHeight = property(nrc_host.LaserCuttingParam_followHeight_get, nrc_host.LaserCuttingParam_followHeight_set)
    freq = property(nrc_host.LaserCuttingParam_freq_get, nrc_host.LaserCuttingParam_freq_set)
    power = property(nrc_host.LaserCuttingParam_power_get, nrc_host.LaserCuttingParam_power_set)
    pressure = property(nrc_host.LaserCuttingParam_pressure_get, nrc_host.LaserCuttingParam_pressure_set)
    note = property(nrc_host.LaserCuttingParam_note_get, nrc_host.LaserCuttingParam_note_set)

    def __init__(self):
        nrc_host.LaserCuttingParam_swiginit(self, nrc_host.new_LaserCuttingParam())
    __swig_destroy__ = nrc_host.delete_LaserCuttingParam

# Register LaserCuttingParam in nrc_host:
nrc_host.LaserCuttingParam_swigregister(LaserCuttingParam)
class LaserCuttingCraftParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    num = property(nrc_host.LaserCuttingCraftParam_num_get, nrc_host.LaserCuttingCraftParam_num_set)
    cut = property(nrc_host.LaserCuttingCraftParam_cut_get, nrc_host.LaserCuttingCraftParam_cut_set)

    def __init__(self):
        nrc_host.LaserCuttingCraftParam_swiginit(self, nrc_host.new_LaserCuttingCraftParam())
    __swig_destroy__ = nrc_host.delete_LaserCuttingCraftParam

# Register LaserCuttingCraftParam in nrc_host:
nrc_host.LaserCuttingCraftParam_swigregister(LaserCuttingCraftParam)
class Curve(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    level = property(nrc_host.Curve_level_get, nrc_host.Curve_level_set)
    x = property(nrc_host.Curve_x_get, nrc_host.Curve_x_set)
    y = property(nrc_host.Curve_y_get, nrc_host.Curve_y_set)

    def __init__(self):
        nrc_host.Curve_swiginit(self, nrc_host.new_Curve())
    __swig_destroy__ = nrc_host.delete_Curve

# Register Curve in nrc_host:
nrc_host.Curve_swigregister(Curve)
class LaserCuttingAnalogMatch(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    laserPower = property(nrc_host.LaserCuttingAnalogMatch_laserPower_get, nrc_host.LaserCuttingAnalogMatch_laserPower_set)
    pressure = property(nrc_host.LaserCuttingAnalogMatch_pressure_get, nrc_host.LaserCuttingAnalogMatch_pressure_set)

    def __init__(self):
        nrc_host.LaserCuttingAnalogMatch_swiginit(self, nrc_host.new_LaserCuttingAnalogMatch())
    __swig_destroy__ = nrc_host.delete_LaserCuttingAnalogMatch

# Register LaserCuttingAnalogMatch in nrc_host:
nrc_host.LaserCuttingAnalogMatch_swigregister(LaserCuttingAnalogMatch)
class LaserCuttingAnalogParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    analogMatch = property(nrc_host.LaserCuttingAnalogParam_analogMatch_get, nrc_host.LaserCuttingAnalogParam_analogMatch_set)

    def __init__(self):
        nrc_host.LaserCuttingAnalogParam_swiginit(self, nrc_host.new_LaserCuttingAnalogParam())
    __swig_destroy__ = nrc_host.delete_LaserCuttingAnalogParam

# Register LaserCuttingAnalogParam in nrc_host:
nrc_host.LaserCuttingAnalogParam_swigregister(LaserCuttingAnalogParam)
class IO(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    AO_laserPower = property(nrc_host.IO_AO_laserPower_get, nrc_host.IO_AO_laserPower_set)
    AO_pressure = property(nrc_host.IO_AO_pressure_get, nrc_host.IO_AO_pressure_set)
    DI_backMiddleArrival = property(nrc_host.IO_DI_backMiddleArrival_get, nrc_host.IO_DI_backMiddleArrival_set)
    DI_capacitance_ = property(nrc_host.IO_DI_capacitance__get, nrc_host.IO_DI_capacitance__set)
    DI_followArrival = property(nrc_host.IO_DI_followArrival_get, nrc_host.IO_DI_followArrival_set)
    DI_liftUpArrival = property(nrc_host.IO_DI_liftUpArrival_get, nrc_host.IO_DI_liftUpArrival_set)
    DI_perforateArrival = property(nrc_host.IO_DI_perforateArrival_get, nrc_host.IO_DI_perforateArrival_set)
    DO_aspiration = property(nrc_host.IO_DO_aspiration_get, nrc_host.IO_DO_aspiration_set)
    DO_backMiddle = property(nrc_host.IO_DO_backMiddle_get, nrc_host.IO_DO_backMiddle_set)
    DO_capacitance_ = property(nrc_host.IO_DO_capacitance__get, nrc_host.IO_DO_capacitance__set)
    DO_follow = property(nrc_host.IO_DO_follow_get, nrc_host.IO_DO_follow_set)
    DO_highPressgas = property(nrc_host.IO_DO_highPressgas_get, nrc_host.IO_DO_highPressgas_set)
    DO_liftUp = property(nrc_host.IO_DO_liftUp_get, nrc_host.IO_DO_liftUp_set)
    DO_lightGate = property(nrc_host.IO_DO_lightGate_get, nrc_host.IO_DO_lightGate_set)
    DO_lowPressgas = property(nrc_host.IO_DO_lowPressgas_get, nrc_host.IO_DO_lowPressgas_set)
    DO_redLight = property(nrc_host.IO_DO_redLight_get, nrc_host.IO_DO_redLight_set)
    pwm_port_ = property(nrc_host.IO_pwm_port__get, nrc_host.IO_pwm_port__set)

    def __init__(self):
        nrc_host.IO_swiginit(self, nrc_host.new_IO())
    __swig_destroy__ = nrc_host.delete_IO

# Register IO in nrc_host:
nrc_host.IO_swigregister(IO)
class Fault(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    input_port = property(nrc_host.Fault_input_port_get, nrc_host.Fault_input_port_set)

    def __init__(self):
        nrc_host.Fault_swiginit(self, nrc_host.new_Fault())
    __swig_destroy__ = nrc_host.delete_Fault

# Register Fault in nrc_host:
nrc_host.Fault_swigregister(Fault)
class LaserCuttingIOParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    io = property(nrc_host.LaserCuttingIOParam_io_get, nrc_host.LaserCuttingIOParam_io_set)
    laser_fault = property(nrc_host.LaserCuttingIOParam_laser_fault_get, nrc_host.LaserCuttingIOParam_laser_fault_set)
    pressure_fault = property(nrc_host.LaserCuttingIOParam_pressure_fault_get, nrc_host.LaserCuttingIOParam_pressure_fault_set)
    regulator_fault = property(nrc_host.LaserCuttingIOParam_regulator_fault_get, nrc_host.LaserCuttingIOParam_regulator_fault_set)
    water_cooler_fault = property(nrc_host.LaserCuttingIOParam_water_cooler_fault_get, nrc_host.LaserCuttingIOParam_water_cooler_fault_set)

    def __init__(self):
        nrc_host.LaserCuttingIOParam_swiginit(self, nrc_host.new_LaserCuttingIOParam())
    __swig_destroy__ = nrc_host.delete_LaserCuttingIOParam

# Register LaserCuttingIOParam in nrc_host:
nrc_host.LaserCuttingIOParam_swigregister(LaserCuttingIOParam)
class CameraList(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    currentName = property(nrc_host.CameraList_currentName_get, nrc_host.CameraList_currentName_set)
    listNum = property(nrc_host.CameraList_listNum_get, nrc_host.CameraList_listNum_set)

    def __init__(self):
        nrc_host.CameraList_swiginit(self, nrc_host.new_CameraList())
    __swig_destroy__ = nrc_host.delete_CameraList

# Register CameraList in nrc_host:
nrc_host.CameraList_swigregister(CameraList)
class Protocol(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    addDataInitialPara = property(nrc_host.Protocol_addDataInitialPara_get, nrc_host.Protocol_addDataInitialPara_set)
    addDataNum = property(nrc_host.Protocol_addDataNum_get, nrc_host.Protocol_addDataNum_set)
    angleUnit = property(nrc_host.Protocol_angleUnit_get, nrc_host.Protocol_angleUnit_set)
    endMark = property(nrc_host.Protocol_endMark_get, nrc_host.Protocol_endMark_set)
    failFlag = property(nrc_host.Protocol_failFlag_get, nrc_host.Protocol_failFlag_set)
    frameHeader = property(nrc_host.Protocol_frameHeader_get, nrc_host.Protocol_frameHeader_set)
    hasTCS = property(nrc_host.Protocol_hasTCS_get, nrc_host.Protocol_hasTCS_set)
    hasUCS = property(nrc_host.Protocol_hasUCS_get, nrc_host.Protocol_hasUCS_set)
    separator = property(nrc_host.Protocol_separator_get, nrc_host.Protocol_separator_set)
    singleTarget = property(nrc_host.Protocol_singleTarget_get, nrc_host.Protocol_singleTarget_set)
    successFlag = property(nrc_host.Protocol_successFlag_get, nrc_host.Protocol_successFlag_set)
    timeOut = property(nrc_host.Protocol_timeOut_get, nrc_host.Protocol_timeOut_set)
    type = property(nrc_host.Protocol_type_get, nrc_host.Protocol_type_set)

    def __init__(self):
        nrc_host.Protocol_swiginit(self, nrc_host.new_Protocol())
    __swig_destroy__ = nrc_host.delete_Protocol

# Register Protocol in nrc_host:
nrc_host.Protocol_swigregister(Protocol)
class Socket(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    IP = property(nrc_host.Socket_IP_get, nrc_host.Socket_IP_set)
    cameraDataType = property(nrc_host.Socket_cameraDataType_get, nrc_host.Socket_cameraDataType_set)
    portNum = property(nrc_host.Socket_portNum_get, nrc_host.Socket_portNum_set)
    portOne = property(nrc_host.Socket_portOne_get, nrc_host.Socket_portOne_set)
    portTwo = property(nrc_host.Socket_portTwo_get, nrc_host.Socket_portTwo_set)
    server = property(nrc_host.Socket_server_get, nrc_host.Socket_server_set)

    def __init__(self):
        nrc_host.Socket_swiginit(self, nrc_host.new_Socket())
    __swig_destroy__ = nrc_host.delete_Socket

# Register Socket in nrc_host:
nrc_host.Socket_swigregister(Socket)
class Trigger(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    IOPort = property(nrc_host.Trigger_IOPort_get, nrc_host.Trigger_IOPort_set)
    duration = property(nrc_host.Trigger_duration_get, nrc_host.Trigger_duration_set)
    intervals = property(nrc_host.Trigger_intervals_get, nrc_host.Trigger_intervals_set)
    triggerMode = property(nrc_host.Trigger_triggerMode_get, nrc_host.Trigger_triggerMode_set)
    triggerOnce = property(nrc_host.Trigger_triggerOnce_get, nrc_host.Trigger_triggerOnce_set)
    triggerStr = property(nrc_host.Trigger_triggerStr_get, nrc_host.Trigger_triggerStr_set)

    def __init__(self):
        nrc_host.Trigger_swiginit(self, nrc_host.new_Trigger())
    __swig_destroy__ = nrc_host.delete_Trigger

# Register Trigger in nrc_host:
nrc_host.Trigger_swigregister(Trigger)
class VisionParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    cameraList = property(nrc_host.VisionParam_cameraList_get, nrc_host.VisionParam_cameraList_set)
    protocol = property(nrc_host.VisionParam_protocol_get, nrc_host.VisionParam_protocol_set)
    socket = property(nrc_host.VisionParam_socket_get, nrc_host.VisionParam_socket_set)
    trigger = property(nrc_host.VisionParam_trigger_get, nrc_host.VisionParam_trigger_set)
    userCoordNum = property(nrc_host.VisionParam_userCoordNum_get, nrc_host.VisionParam_userCoordNum_set)

    def __init__(self):
        nrc_host.VisionParam_swiginit(self, nrc_host.new_VisionParam())
    __swig_destroy__ = nrc_host.delete_VisionParam

# Register VisionParam in nrc_host:
nrc_host.VisionParam_swigregister(VisionParam)
class VisionRange(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    maxX = property(nrc_host.VisionRange_maxX_get, nrc_host.VisionRange_maxX_set)
    maxY = property(nrc_host.VisionRange_maxY_get, nrc_host.VisionRange_maxY_set)
    maxZ = property(nrc_host.VisionRange_maxZ_get, nrc_host.VisionRange_maxZ_set)
    minX = property(nrc_host.VisionRange_minX_get, nrc_host.VisionRange_minX_set)
    minY = property(nrc_host.VisionRange_minY_get, nrc_host.VisionRange_minY_set)
    minZ = property(nrc_host.VisionRange_minZ_get, nrc_host.VisionRange_minZ_set)

    def __init__(self):
        nrc_host.VisionRange_swiginit(self, nrc_host.new_VisionRange())
    __swig_destroy__ = nrc_host.delete_VisionRange

# Register VisionRange in nrc_host:
nrc_host.VisionRange_swigregister(VisionRange)
class Excursion(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    Xexcursion = property(nrc_host.Excursion_Xexcursion_get, nrc_host.Excursion_Xexcursion_set)
    Yexcursion = property(nrc_host.Excursion_Yexcursion_get, nrc_host.Excursion_Yexcursion_set)
    Zexcursion = property(nrc_host.Excursion_Zexcursion_get, nrc_host.Excursion_Zexcursion_set)
    angle = property(nrc_host.Excursion_angle_get, nrc_host.Excursion_angle_set)

    def __init__(self):
        nrc_host.Excursion_swiginit(self, nrc_host.new_Excursion())
    __swig_destroy__ = nrc_host.delete_Excursion

# Register Excursion in nrc_host:
nrc_host.Excursion_swigregister(Excursion)
class Position(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    angleDirection = property(nrc_host.Position_angleDirection_get, nrc_host.Position_angleDirection_set)
    cameraData = property(nrc_host.Position_cameraData_get, nrc_host.Position_cameraData_set)
    cameraPoint = property(nrc_host.Position_cameraPoint_get, nrc_host.Position_cameraPoint_set)
    datumPoint = property(nrc_host.Position_datumPoint_get, nrc_host.Position_datumPoint_set)
    datumPointDeg = property(nrc_host.Position_datumPointDeg_get, nrc_host.Position_datumPointDeg_set)
    excursion = property(nrc_host.Position_excursion_get, nrc_host.Position_excursion_set)
    recvPointsType = property(nrc_host.Position_recvPointsType_get, nrc_host.Position_recvPointsType_set)
    sampleData = property(nrc_host.Position_sampleData_get, nrc_host.Position_sampleData_set)
    scale = property(nrc_host.Position_scale_get, nrc_host.Position_scale_set)

    def __init__(self):
        nrc_host.Position_swiginit(self, nrc_host.new_Position())
    __swig_destroy__ = nrc_host.delete_Position

# Register Position in nrc_host:
nrc_host.Position_swigregister(Position)
class VisionPositionParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    position = property(nrc_host.VisionPositionParam_position_get, nrc_host.VisionPositionParam_position_set)
    protocol = property(nrc_host.VisionPositionParam_protocol_get, nrc_host.VisionPositionParam_protocol_set)

    def __init__(self):
        nrc_host.VisionPositionParam_swiginit(self, nrc_host.new_VisionPositionParam())
    __swig_destroy__ = nrc_host.delete_VisionPositionParam

# Register VisionPositionParam in nrc_host:
nrc_host.VisionPositionParam_swigregister(VisionPositionParam)
class CalibrationPoint(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    pixel_pos = property(nrc_host.CalibrationPoint_pixel_pos_get, nrc_host.CalibrationPoint_pixel_pos_set)
    pixel_pos_deg = property(nrc_host.CalibrationPoint_pixel_pos_deg_get, nrc_host.CalibrationPoint_pixel_pos_deg_set)
    robot_pos = property(nrc_host.CalibrationPoint_robot_pos_get, nrc_host.CalibrationPoint_robot_pos_set)
    robot_pos_deg = property(nrc_host.CalibrationPoint_robot_pos_deg_get, nrc_host.CalibrationPoint_robot_pos_deg_set)

    def __init__(self):
        nrc_host.CalibrationPoint_swiginit(self, nrc_host.new_CalibrationPoint())
    __swig_destroy__ = nrc_host.delete_CalibrationPoint

# Register CalibrationPoint in nrc_host:
nrc_host.CalibrationPoint_swigregister(CalibrationPoint)
class Calibration(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    calibrated = property(nrc_host.Calibration_calibrated_get, nrc_host.Calibration_calibrated_set)
    point = property(nrc_host.Calibration_point_get, nrc_host.Calibration_point_set)
    point_num = property(nrc_host.Calibration_point_num_get, nrc_host.Calibration_point_num_set)

    def addCalibrationPoint(self, pos):
        return nrc_host.Calibration_addCalibrationPoint(self, pos)

    def getPoint(self, index):
        return nrc_host.Calibration_getPoint(self, index)

    def __init__(self, num_points=6):
        nrc_host.Calibration_swiginit(self, nrc_host.new_Calibration(num_points))
    __swig_destroy__ = nrc_host.delete_Calibration

# Register Calibration in nrc_host:
nrc_host.Calibration_swigregister(Calibration)
class VisionCalibrationData(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    visionNum = property(nrc_host.VisionCalibrationData_visionNum_get, nrc_host.VisionCalibrationData_visionNum_set)
    calibration = property(nrc_host.VisionCalibrationData_calibration_get, nrc_host.VisionCalibrationData_calibration_set)

    def __init__(self, num_points=6):
        nrc_host.VisionCalibrationData_swiginit(self, nrc_host.new_VisionCalibrationData(num_points))
    __swig_destroy__ = nrc_host.delete_VisionCalibrationData

# Register VisionCalibrationData in nrc_host:
nrc_host.VisionCalibrationData_swigregister(VisionCalibrationData)
class ArcParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    weldVoltage = property(nrc_host.ArcParam_weldVoltage_get, nrc_host.ArcParam_weldVoltage_set)
    weldCurrent = property(nrc_host.ArcParam_weldCurrent_get, nrc_host.ArcParam_weldCurrent_set)
    arcOnCurrent = property(nrc_host.ArcParam_arcOnCurrent_get, nrc_host.ArcParam_arcOnCurrent_set)
    arcOnVoltage = property(nrc_host.ArcParam_arcOnVoltage_get, nrc_host.ArcParam_arcOnVoltage_set)
    arcOnTime = property(nrc_host.ArcParam_arcOnTime_get, nrc_host.ArcParam_arcOnTime_set)
    arcOnRampEnable = property(nrc_host.ArcParam_arcOnRampEnable_get, nrc_host.ArcParam_arcOnRampEnable_set)
    arcOnRampMode = property(nrc_host.ArcParam_arcOnRampMode_get, nrc_host.ArcParam_arcOnRampMode_set)
    arcOnRampTime = property(nrc_host.ArcParam_arcOnRampTime_get, nrc_host.ArcParam_arcOnRampTime_set)
    arcOffCurrent = property(nrc_host.ArcParam_arcOffCurrent_get, nrc_host.ArcParam_arcOffCurrent_set)
    arcOffVoltage = property(nrc_host.ArcParam_arcOffVoltage_get, nrc_host.ArcParam_arcOffVoltage_set)
    arcOffTime = property(nrc_host.ArcParam_arcOffTime_get, nrc_host.ArcParam_arcOffTime_set)
    arcOffRampEnable = property(nrc_host.ArcParam_arcOffRampEnable_get, nrc_host.ArcParam_arcOffRampEnable_set)
    arcOffRampMode = property(nrc_host.ArcParam_arcOffRampMode_get, nrc_host.ArcParam_arcOffRampMode_set)
    arcOffRampTime = property(nrc_host.ArcParam_arcOffRampTime_get, nrc_host.ArcParam_arcOffRampTime_set)

    def __init__(self):
        nrc_host.ArcParam_swiginit(self, nrc_host.new_ArcParam())
    __swig_destroy__ = nrc_host.delete_ArcParam

# Register ArcParam in nrc_host:
nrc_host.ArcParam_swigregister(ArcParam)
class WaveParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    type = property(nrc_host.WaveParam_type_get, nrc_host.WaveParam_type_set)
    swingFreq = property(nrc_host.WaveParam_swingFreq_get, nrc_host.WaveParam_swingFreq_set)
    swingAmplitude = property(nrc_host.WaveParam_swingAmplitude_get, nrc_host.WaveParam_swingAmplitude_set)
    radius = property(nrc_host.WaveParam_radius_get, nrc_host.WaveParam_radius_set)
    LTypeAngle = property(nrc_host.WaveParam_LTypeAngle_get, nrc_host.WaveParam_LTypeAngle_set)
    moveWhenEdgeStay = property(nrc_host.WaveParam_moveWhenEdgeStay_get, nrc_host.WaveParam_moveWhenEdgeStay_set)
    leftStayTime = property(nrc_host.WaveParam_leftStayTime_get, nrc_host.WaveParam_leftStayTime_set)
    rightStayTime = property(nrc_host.WaveParam_rightStayTime_get, nrc_host.WaveParam_rightStayTime_set)
    initialDir = property(nrc_host.WaveParam_initialDir_get, nrc_host.WaveParam_initialDir_set)
    horizontalDeflection = property(nrc_host.WaveParam_horizontalDeflection_get, nrc_host.WaveParam_horizontalDeflection_set)
    verticalDeflection = property(nrc_host.WaveParam_verticalDeflection_get, nrc_host.WaveParam_verticalDeflection_set)

    def __init__(self):
        nrc_host.WaveParam_swiginit(self, nrc_host.new_WaveParam())
    __swig_destroy__ = nrc_host.delete_WaveParam

# Register WaveParam in nrc_host:
nrc_host.WaveParam_swigregister(WaveParam)
class WeldState(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    pistolSwitch = property(nrc_host.WeldState_pistolSwitch_get, nrc_host.WeldState_pistolSwitch_set)
    arcingSuccess = property(nrc_host.WeldState_arcingSuccess_get, nrc_host.WeldState_arcingSuccess_set)
    handWireFeed = property(nrc_host.WeldState_handWireFeed_get, nrc_host.WeldState_handWireFeed_set)
    weldCurrent = property(nrc_host.WeldState_weldCurrent_get, nrc_host.WeldState_weldCurrent_set)
    weldVoltage = property(nrc_host.WeldState_weldVoltage_get, nrc_host.WeldState_weldVoltage_set)
    weldTime = property(nrc_host.WeldState_weldTime_get, nrc_host.WeldState_weldTime_set)
    weldPWM = property(nrc_host.WeldState_weldPWM_get, nrc_host.WeldState_weldPWM_set)

    def __init__(self):
        nrc_host.WeldState_swiginit(self, nrc_host.new_WeldState())
    __swig_destroy__ = nrc_host.delete_WeldState

# Register WeldState in nrc_host:
nrc_host.WeldState_swigregister(WeldState)
EXCEPTION = nrc_host.EXCEPTION
OPERATION_NOT_ALLOWED = nrc_host.OPERATION_NOT_ALLOWED
PARAM_ERR = nrc_host.PARAM_ERR
DISCONNECT = nrc_host.DISCONNECT
RECEIVE_FAILED = nrc_host.RECEIVE_FAILED
SUCCESS = nrc_host.SUCCESS
PosType_data = nrc_host.PosType_data
PosType_PType = nrc_host.PosType_PType
PosType_E_TYPE = nrc_host.PosType_E_TYPE
PosType_RP_TYPE = nrc_host.PosType_RP_TYPE
PosType_AP_TYPE = nrc_host.PosType_AP_TYPE
PosType_GPType = nrc_host.PosType_GPType
PosType_GEType = nrc_host.PosType_GEType
class MoveCmd(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    targetPosType = property(nrc_host.MoveCmd_targetPosType_get, nrc_host.MoveCmd_targetPosType_set)
    targetPosValue = property(nrc_host.MoveCmd_targetPosValue_get, nrc_host.MoveCmd_targetPosValue_set)
    targetPosName = property(nrc_host.MoveCmd_targetPosName_get, nrc_host.MoveCmd_targetPosName_set)
    coord = property(nrc_host.MoveCmd_coord_get, nrc_host.MoveCmd_coord_set)
    velocity = property(nrc_host.MoveCmd_velocity_get, nrc_host.MoveCmd_velocity_set)
    acc = property(nrc_host.MoveCmd_acc_get, nrc_host.MoveCmd_acc_set)
    dec = property(nrc_host.MoveCmd_dec_get, nrc_host.MoveCmd_dec_set)
    pl = property(nrc_host.MoveCmd_pl_get, nrc_host.MoveCmd_pl_set)
    time = property(nrc_host.MoveCmd_time_get, nrc_host.MoveCmd_time_set)
    toolNum = property(nrc_host.MoveCmd_toolNum_get, nrc_host.MoveCmd_toolNum_set)
    userNum = property(nrc_host.MoveCmd_userNum_get, nrc_host.MoveCmd_userNum_set)
    posidtype = property(nrc_host.MoveCmd_posidtype_get, nrc_host.MoveCmd_posidtype_set)
    configuration = property(nrc_host.MoveCmd_configuration_get, nrc_host.MoveCmd_configuration_set)

    def __init__(self):
        nrc_host.MoveCmd_swiginit(self, nrc_host.new_MoveCmd())
    __swig_destroy__ = nrc_host.delete_MoveCmd

# Register MoveCmd in nrc_host:
nrc_host.MoveCmd_swigregister(MoveCmd)
cvar = nrc_host.cvar
const_robotNum = cvar.const_robotNum

EQUAL_TO = nrc_host.EQUAL_TO
LESS = nrc_host.LESS
GREATER = nrc_host.GREATER
LESS_EQUAL = nrc_host.LESS_EQUAL
GREATER_EQUAL = nrc_host.GREATER_EQUAL
NOT_EQUAL_TO = nrc_host.NOT_EQUAL_TO
class ParaGroup(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    data = property(nrc_host.ParaGroup_data_get, nrc_host.ParaGroup_data_set)
    secondvalue = property(nrc_host.ParaGroup_secondvalue_get, nrc_host.ParaGroup_secondvalue_set)
    value = property(nrc_host.ParaGroup_value_get, nrc_host.ParaGroup_value_set)
    varname = property(nrc_host.ParaGroup_varname_get, nrc_host.ParaGroup_varname_set)

    def __init__(self):
        nrc_host.ParaGroup_swiginit(self, nrc_host.new_ParaGroup())
    __swig_destroy__ = nrc_host.delete_ParaGroup

# Register ParaGroup in nrc_host:
nrc_host.ParaGroup_swigregister(ParaGroup)
class Condition(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    desValue = property(nrc_host.Condition_desValue_get, nrc_host.Condition_desValue_set)
    key = property(nrc_host.Condition_key_get, nrc_host.Condition_key_set)
    logicType = property(nrc_host.Condition_logicType_get, nrc_host.Condition_logicType_set)
    paraGroupOne = property(nrc_host.Condition_paraGroupOne_get, nrc_host.Condition_paraGroupOne_set)
    paraGroupTwo = property(nrc_host.Condition_paraGroupTwo_get, nrc_host.Condition_paraGroupTwo_set)

    def __init__(self):
        nrc_host.Condition_swiginit(self, nrc_host.new_Condition())
    __swig_destroy__ = nrc_host.delete_Condition

# Register Condition in nrc_host:
nrc_host.Condition_swigregister(Condition)
class PositionData(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    key = property(nrc_host.PositionData_key_get, nrc_host.PositionData_key_set)
    posData = property(nrc_host.PositionData_posData_get, nrc_host.PositionData_posData_set)
    coord = property(nrc_host.PositionData_coord_get, nrc_host.PositionData_coord_set)
    toolNum = property(nrc_host.PositionData_toolNum_get, nrc_host.PositionData_toolNum_set)
    userNum = property(nrc_host.PositionData_userNum_get, nrc_host.PositionData_userNum_set)
    type = property(nrc_host.PositionData_type_get, nrc_host.PositionData_type_set)

    @staticmethod
    def determineType(key):
        return nrc_host.PositionData_determineType(key)

    def __init__(self):
        nrc_host.PositionData_swiginit(self, nrc_host.new_PositionData())
    __swig_destroy__ = nrc_host.delete_PositionData

# Register PositionData in nrc_host:
nrc_host.PositionData_swigregister(PositionData)
class IOCommandParams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    groupType = property(nrc_host.IOCommandParams_groupType_get, nrc_host.IOCommandParams_groupType_set)
    errorHanding = property(nrc_host.IOCommandParams_errorHanding_get, nrc_host.IOCommandParams_errorHanding_set)
    paraGroupNum = property(nrc_host.IOCommandParams_paraGroupNum_get, nrc_host.IOCommandParams_paraGroupNum_set)
    paraGroupTime = property(nrc_host.IOCommandParams_paraGroupTime_get, nrc_host.IOCommandParams_paraGroupTime_set)
    paraGroupValue = property(nrc_host.IOCommandParams_paraGroupValue_get, nrc_host.IOCommandParams_paraGroupValue_set)

    def __init__(self):
        nrc_host.IOCommandParams_swiginit(self, nrc_host.new_IOCommandParams())
    __swig_destroy__ = nrc_host.delete_IOCommandParams

# Register IOCommandParams in nrc_host:
nrc_host.IOCommandParams_swigregister(IOCommandParams)
class OffsetCommandParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    calData = property(nrc_host.OffsetCommandParam_calData_get, nrc_host.OffsetCommandParam_calData_set)
    coord = property(nrc_host.OffsetCommandParam_coord_get, nrc_host.OffsetCommandParam_coord_set)
    datatype = property(nrc_host.OffsetCommandParam_datatype_get, nrc_host.OffsetCommandParam_datatype_set)
    tool = property(nrc_host.OffsetCommandParam_tool_get, nrc_host.OffsetCommandParam_tool_set)
    user = property(nrc_host.OffsetCommandParam_user_get, nrc_host.OffsetCommandParam_user_set)

    def __init__(self):
        nrc_host.OffsetCommandParam_swiginit(self, nrc_host.new_OffsetCommandParam())
    __swig_destroy__ = nrc_host.delete_OffsetCommandParam

# Register OffsetCommandParam in nrc_host:
nrc_host.OffsetCommandParam_swigregister(OffsetCommandParam)
NOTYPE = nrc_host.NOTYPE
SIX_AXLE_GENERAL = nrc_host.SIX_AXLE_GENERAL
FOUR_AXLE_SCARA = nrc_host.FOUR_AXLE_SCARA
FOUR_AXLE_STACK = nrc_host.FOUR_AXLE_STACK
FOUR_AXLE_GENERAL = nrc_host.FOUR_AXLE_GENERAL
ONE_AXLE_GENERAL = nrc_host.ONE_AXLE_GENERAL
FIVE_AXLE_GENERAL = nrc_host.FIVE_AXLE_GENERAL
SIX_AXLE_ONE_GENERAL = nrc_host.SIX_AXLE_ONE_GENERAL
TWO_AXLE_SCARA = nrc_host.TWO_AXLE_SCARA
THREE_AXLE_SCARA = nrc_host.THREE_AXLE_SCARA
THREE_AXLE_ANGLE = nrc_host.THREE_AXLE_ANGLE
THREE_AXLE_ABNORMITY = nrc_host.THREE_AXLE_ABNORMITY
SEVEN_AXLE_GENERAL = nrc_host.SEVEN_AXLE_GENERAL
FOUR_AXLE_SCARA_ABNORMITY = nrc_host.FOUR_AXLE_SCARA_ABNORMITY
FOUR_AXLE_PALLET_1 = nrc_host.FOUR_AXLE_PALLET_1
SIX_AXLE_SPRAY = nrc_host.SIX_AXLE_SPRAY
FOUR_AXLE_ANGLE = nrc_host.FOUR_AXLE_ANGLE
FOUR_AXLE_POLAR_ABNORMITY = nrc_host.FOUR_AXLE_POLAR_ABNORMITY
SIX_AXLE_ABNORMITY = nrc_host.SIX_AXLE_ABNORMITY
GANTRY_WELD = nrc_host.GANTRY_WELD
R_DELTA = nrc_host.R_DELTA
R_WINE_CHAMFER = nrc_host.R_WINE_CHAMFER
R_GANTRY_WELD_2_ = nrc_host.R_GANTRY_WELD_2_
R_GENERAL_5S_COLLABORATIVE_ = nrc_host.R_GENERAL_5S_COLLABORATIVE_
FOUR_AXLE_SCARA_ABNORMITY_3_ = nrc_host.FOUR_AXLE_SCARA_ABNORMITY_3_
class ToolParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    X = property(nrc_host.ToolParam_X_get, nrc_host.ToolParam_X_set)
    Y = property(nrc_host.ToolParam_Y_get, nrc_host.ToolParam_Y_set)
    Z = property(nrc_host.ToolParam_Z_get, nrc_host.ToolParam_Z_set)
    A = property(nrc_host.ToolParam_A_get, nrc_host.ToolParam_A_set)
    B = property(nrc_host.ToolParam_B_get, nrc_host.ToolParam_B_set)
    C = property(nrc_host.ToolParam_C_get, nrc_host.ToolParam_C_set)
    payloadMass = property(nrc_host.ToolParam_payloadMass_get, nrc_host.ToolParam_payloadMass_set)
    payloadInertia = property(nrc_host.ToolParam_payloadInertia_get, nrc_host.ToolParam_payloadInertia_set)
    payloadMassCenter_X = property(nrc_host.ToolParam_payloadMassCenter_X_get, nrc_host.ToolParam_payloadMassCenter_X_set)
    payloadMassCenter_Y = property(nrc_host.ToolParam_payloadMassCenter_Y_get, nrc_host.ToolParam_payloadMassCenter_Y_set)
    payloadMassCenter_Z = property(nrc_host.ToolParam_payloadMassCenter_Z_get, nrc_host.ToolParam_payloadMassCenter_Z_set)

    def __init__(self):
        nrc_host.ToolParam_swiginit(self, nrc_host.new_ToolParam())
    __swig_destroy__ = nrc_host.delete_ToolParam

# Register ToolParam in nrc_host:
nrc_host.ToolParam_swigregister(ToolParam)
class RobotDHParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    L1 = property(nrc_host.RobotDHParam_L1_get, nrc_host.RobotDHParam_L1_set)
    L2 = property(nrc_host.RobotDHParam_L2_get, nrc_host.RobotDHParam_L2_set)
    L3 = property(nrc_host.RobotDHParam_L3_get, nrc_host.RobotDHParam_L3_set)
    L4 = property(nrc_host.RobotDHParam_L4_get, nrc_host.RobotDHParam_L4_set)
    L5 = property(nrc_host.RobotDHParam_L5_get, nrc_host.RobotDHParam_L5_set)
    L6 = property(nrc_host.RobotDHParam_L6_get, nrc_host.RobotDHParam_L6_set)
    L7 = property(nrc_host.RobotDHParam_L7_get, nrc_host.RobotDHParam_L7_set)
    L8 = property(nrc_host.RobotDHParam_L8_get, nrc_host.RobotDHParam_L8_set)
    L9 = property(nrc_host.RobotDHParam_L9_get, nrc_host.RobotDHParam_L9_set)
    L10 = property(nrc_host.RobotDHParam_L10_get, nrc_host.RobotDHParam_L10_set)
    L11 = property(nrc_host.RobotDHParam_L11_get, nrc_host.RobotDHParam_L11_set)
    L12 = property(nrc_host.RobotDHParam_L12_get, nrc_host.RobotDHParam_L12_set)
    L13 = property(nrc_host.RobotDHParam_L13_get, nrc_host.RobotDHParam_L13_set)
    L14 = property(nrc_host.RobotDHParam_L14_get, nrc_host.RobotDHParam_L14_set)
    L15 = property(nrc_host.RobotDHParam_L15_get, nrc_host.RobotDHParam_L15_set)
    L16 = property(nrc_host.RobotDHParam_L16_get, nrc_host.RobotDHParam_L16_set)
    L17 = property(nrc_host.RobotDHParam_L17_get, nrc_host.RobotDHParam_L17_set)
    L18 = property(nrc_host.RobotDHParam_L18_get, nrc_host.RobotDHParam_L18_set)
    L19 = property(nrc_host.RobotDHParam_L19_get, nrc_host.RobotDHParam_L19_set)
    L20 = property(nrc_host.RobotDHParam_L20_get, nrc_host.RobotDHParam_L20_set)
    Couple_Coe_1_2 = property(nrc_host.RobotDHParam_Couple_Coe_1_2_get, nrc_host.RobotDHParam_Couple_Coe_1_2_set)
    Couple_Coe_2_3 = property(nrc_host.RobotDHParam_Couple_Coe_2_3_get, nrc_host.RobotDHParam_Couple_Coe_2_3_set)
    Couple_Coe_3_2 = property(nrc_host.RobotDHParam_Couple_Coe_3_2_get, nrc_host.RobotDHParam_Couple_Coe_3_2_set)
    Couple_Coe_3_4 = property(nrc_host.RobotDHParam_Couple_Coe_3_4_get, nrc_host.RobotDHParam_Couple_Coe_3_4_set)
    Couple_Coe_4_5 = property(nrc_host.RobotDHParam_Couple_Coe_4_5_get, nrc_host.RobotDHParam_Couple_Coe_4_5_set)
    Couple_Coe_4_6 = property(nrc_host.RobotDHParam_Couple_Coe_4_6_get, nrc_host.RobotDHParam_Couple_Coe_4_6_set)
    Couple_Coe_5_6 = property(nrc_host.RobotDHParam_Couple_Coe_5_6_get, nrc_host.RobotDHParam_Couple_Coe_5_6_set)
    dynamicLimit_max = property(nrc_host.RobotDHParam_dynamicLimit_max_get, nrc_host.RobotDHParam_dynamicLimit_max_set)
    dynamicLimit_min = property(nrc_host.RobotDHParam_dynamicLimit_min_get, nrc_host.RobotDHParam_dynamicLimit_min_set)
    pitch = property(nrc_host.RobotDHParam_pitch_get, nrc_host.RobotDHParam_pitch_set)
    sliding_lead_value = property(nrc_host.RobotDHParam_sliding_lead_value_get, nrc_host.RobotDHParam_sliding_lead_value_set)
    uplift_lead_value = property(nrc_host.RobotDHParam_uplift_lead_value_get, nrc_host.RobotDHParam_uplift_lead_value_set)
    spray_distance = property(nrc_host.RobotDHParam_spray_distance_get, nrc_host.RobotDHParam_spray_distance_set)
    threeAxisDirection = property(nrc_host.RobotDHParam_threeAxisDirection_get, nrc_host.RobotDHParam_threeAxisDirection_set)
    fiveAxisDirection = property(nrc_host.RobotDHParam_fiveAxisDirection_get, nrc_host.RobotDHParam_fiveAxisDirection_set)
    twoAxisConversionRatio = property(nrc_host.RobotDHParam_twoAxisConversionRatio_get, nrc_host.RobotDHParam_twoAxisConversionRatio_set)
    threeAxisConversionRatio = property(nrc_host.RobotDHParam_threeAxisConversionRatio_get, nrc_host.RobotDHParam_threeAxisConversionRatio_set)
    amplificationRatio = property(nrc_host.RobotDHParam_amplificationRatio_get, nrc_host.RobotDHParam_amplificationRatio_set)
    conversionratio_x = property(nrc_host.RobotDHParam_conversionratio_x_get, nrc_host.RobotDHParam_conversionratio_x_set)
    conversionratio_y = property(nrc_host.RobotDHParam_conversionratio_y_get, nrc_host.RobotDHParam_conversionratio_y_set)
    conversionratio_z = property(nrc_host.RobotDHParam_conversionratio_z_get, nrc_host.RobotDHParam_conversionratio_z_set)
    conversionratio_J1 = property(nrc_host.RobotDHParam_conversionratio_J1_get, nrc_host.RobotDHParam_conversionratio_J1_set)
    conversionratio_J2 = property(nrc_host.RobotDHParam_conversionratio_J2_get, nrc_host.RobotDHParam_conversionratio_J2_set)
    conversionratio_J3 = property(nrc_host.RobotDHParam_conversionratio_J3_get, nrc_host.RobotDHParam_conversionratio_J3_set)
    upsideDown = property(nrc_host.RobotDHParam_upsideDown_get, nrc_host.RobotDHParam_upsideDown_set)

    def __init__(self):
        nrc_host.RobotDHParam_swiginit(self, nrc_host.new_RobotDHParam())
    __swig_destroy__ = nrc_host.delete_RobotDHParam

# Register RobotDHParam in nrc_host:
nrc_host.RobotDHParam_swigregister(RobotDHParam)
class ServoMovePara(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    clearBuffer = property(nrc_host.ServoMovePara_clearBuffer_get, nrc_host.ServoMovePara_clearBuffer_set)
    targetMode = property(nrc_host.ServoMovePara_targetMode_get, nrc_host.ServoMovePara_targetMode_set)
    sendMode = property(nrc_host.ServoMovePara_sendMode_get, nrc_host.ServoMovePara_sendMode_set)
    runMode = property(nrc_host.ServoMovePara_runMode_get, nrc_host.ServoMovePara_runMode_set)
    sum = property(nrc_host.ServoMovePara_sum_get, nrc_host.ServoMovePara_sum_set)
    count = property(nrc_host.ServoMovePara_count_get, nrc_host.ServoMovePara_count_set)
    coord = property(nrc_host.ServoMovePara_coord_get, nrc_host.ServoMovePara_coord_set)
    extMove = property(nrc_host.ServoMovePara_extMove_get, nrc_host.ServoMovePara_extMove_set)
    size = property(nrc_host.ServoMovePara_size_get, nrc_host.ServoMovePara_size_set)
    pos = property(nrc_host.ServoMovePara_pos_get, nrc_host.ServoMovePara_pos_set)
    timeStamp = property(nrc_host.ServoMovePara_timeStamp_get, nrc_host.ServoMovePara_timeStamp_set)

    def __init__(self):
        nrc_host.ServoMovePara_swiginit(self, nrc_host.new_ServoMovePara())
    __swig_destroy__ = nrc_host.delete_ServoMovePara

# Register ServoMovePara in nrc_host:
nrc_host.ServoMovePara_swigregister(ServoMovePara)
class RobotJointParam(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    reducRatio = property(nrc_host.RobotJointParam_reducRatio_get, nrc_host.RobotJointParam_reducRatio_set)
    encoderResolution = property(nrc_host.RobotJointParam_encoderResolution_get, nrc_host.RobotJointParam_encoderResolution_set)
    posSWLimit = property(nrc_host.RobotJointParam_posSWLimit_get, nrc_host.RobotJointParam_posSWLimit_set)
    negSWLimit = property(nrc_host.RobotJointParam_negSWLimit_get, nrc_host.RobotJointParam_negSWLimit_set)
    ratedRotSpeed = property(nrc_host.RobotJointParam_ratedRotSpeed_get, nrc_host.RobotJointParam_ratedRotSpeed_set)
    ratedDeRotSpeed = property(nrc_host.RobotJointParam_ratedDeRotSpeed_get, nrc_host.RobotJointParam_ratedDeRotSpeed_set)
    maxRotSpeed = property(nrc_host.RobotJointParam_maxRotSpeed_get, nrc_host.RobotJointParam_maxRotSpeed_set)
    maxDeRotSpeed = property(nrc_host.RobotJointParam_maxDeRotSpeed_get, nrc_host.RobotJointParam_maxDeRotSpeed_set)
    ratedVel = property(nrc_host.RobotJointParam_ratedVel_get, nrc_host.RobotJointParam_ratedVel_set)
    deRatedVel = property(nrc_host.RobotJointParam_deRatedVel_get, nrc_host.RobotJointParam_deRatedVel_set)
    maxAcc = property(nrc_host.RobotJointParam_maxAcc_get, nrc_host.RobotJointParam_maxAcc_set)
    maxDecel = property(nrc_host.RobotJointParam_maxDecel_get, nrc_host.RobotJointParam_maxDecel_set)
    direction = property(nrc_host.RobotJointParam_direction_get, nrc_host.RobotJointParam_direction_set)

    def __init__(self):
        nrc_host.RobotJointParam_swiginit(self, nrc_host.new_RobotJointParam())
    __swig_destroy__ = nrc_host.delete_RobotJointParam

# Register RobotJointParam in nrc_host:
nrc_host.RobotJointParam_swigregister(RobotJointParam)
class AlarmdIO(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    msgType = property(nrc_host.AlarmdIO_msgType_get, nrc_host.AlarmdIO_msgType_set)
    value = property(nrc_host.AlarmdIO_value_get, nrc_host.AlarmdIO_value_set)
    enable = property(nrc_host.AlarmdIO_enable_get, nrc_host.AlarmdIO_enable_set)
    msg = property(nrc_host.AlarmdIO_msg_get, nrc_host.AlarmdIO_msg_set)

    def __init__(self):
        nrc_host.AlarmdIO_swiginit(self, nrc_host.new_AlarmdIO())
    __swig_destroy__ = nrc_host.delete_AlarmdIO

# Register AlarmdIO in nrc_host:
nrc_host.AlarmdIO_swigregister(AlarmdIO)
class RemoteProgram(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    port = property(nrc_host.RemoteProgram_port_get, nrc_host.RemoteProgram_port_set)
    value = property(nrc_host.RemoteProgram_value_get, nrc_host.RemoteProgram_value_set)

    def __init__(self):
        nrc_host.RemoteProgram_swiginit(self, nrc_host.new_RemoteProgram())
    __swig_destroy__ = nrc_host.delete_RemoteProgram

# Register RemoteProgram in nrc_host:
nrc_host.RemoteProgram_swigregister(RemoteProgram)
class RemoteControl(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    clearStashPort = property(nrc_host.RemoteControl_clearStashPort_get, nrc_host.RemoteControl_clearStashPort_set)
    faultResetPort = property(nrc_host.RemoteControl_faultResetPort_get, nrc_host.RemoteControl_faultResetPort_set)
    pausePort = property(nrc_host.RemoteControl_pausePort_get, nrc_host.RemoteControl_pausePort_set)
    startPort = property(nrc_host.RemoteControl_startPort_get, nrc_host.RemoteControl_startPort_set)
    stopPort = property(nrc_host.RemoteControl_stopPort_get, nrc_host.RemoteControl_stopPort_set)
    clearStashValue = property(nrc_host.RemoteControl_clearStashValue_get, nrc_host.RemoteControl_clearStashValue_set)
    faultResetValue = property(nrc_host.RemoteControl_faultResetValue_get, nrc_host.RemoteControl_faultResetValue_set)
    pauseValue = property(nrc_host.RemoteControl_pauseValue_get, nrc_host.RemoteControl_pauseValue_set)
    startValue = property(nrc_host.RemoteControl_startValue_get, nrc_host.RemoteControl_startValue_set)
    stopValue = property(nrc_host.RemoteControl_stopValue_get, nrc_host.RemoteControl_stopValue_set)
    program = property(nrc_host.RemoteControl_program_get, nrc_host.RemoteControl_program_set)

    def __init__(self):
        nrc_host.RemoteControl_swiginit(self, nrc_host.new_RemoteControl())
    __swig_destroy__ = nrc_host.delete_RemoteControl

# Register RemoteControl in nrc_host:
nrc_host.RemoteControl_swigregister(RemoteControl)
class RemoteProgramSetting(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    job = property(nrc_host.RemoteProgramSetting_job_get, nrc_host.RemoteProgramSetting_job_set)
    times = property(nrc_host.RemoteProgramSetting_times_get, nrc_host.RemoteProgramSetting_times_set)

    def __init__(self):
        nrc_host.RemoteProgramSetting_swiginit(self, nrc_host.new_RemoteProgramSetting())
    __swig_destroy__ = nrc_host.delete_RemoteProgramSetting

# Register RemoteProgramSetting in nrc_host:
nrc_host.RemoteProgramSetting_swigregister(RemoteProgramSetting)
class SafeIO(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    quickStopPort1 = property(nrc_host.SafeIO_quickStopPort1_get, nrc_host.SafeIO_quickStopPort1_set)
    quickStopPort2 = property(nrc_host.SafeIO_quickStopPort2_get, nrc_host.SafeIO_quickStopPort2_set)
    quickStopValue1 = property(nrc_host.SafeIO_quickStopValue1_get, nrc_host.SafeIO_quickStopValue1_set)
    quickStopValue2 = property(nrc_host.SafeIO_quickStopValue2_get, nrc_host.SafeIO_quickStopValue2_set)
    quickStopEnable = property(nrc_host.SafeIO_quickStopEnable_get, nrc_host.SafeIO_quickStopEnable_set)
    quickStopShied1 = property(nrc_host.SafeIO_quickStopShied1_get, nrc_host.SafeIO_quickStopShied1_set)
    quickStopShied2 = property(nrc_host.SafeIO_quickStopShied2_get, nrc_host.SafeIO_quickStopShied2_set)
    quickStopTime = property(nrc_host.SafeIO_quickStopTime_get, nrc_host.SafeIO_quickStopTime_set)
    quickStopShiedTime = property(nrc_host.SafeIO_quickStopShiedTime_get, nrc_host.SafeIO_quickStopShiedTime_set)
    screenPort1 = property(nrc_host.SafeIO_screenPort1_get, nrc_host.SafeIO_screenPort1_set)
    screenPort2 = property(nrc_host.SafeIO_screenPort2_get, nrc_host.SafeIO_screenPort2_set)
    screenValue1 = property(nrc_host.SafeIO_screenValue1_get, nrc_host.SafeIO_screenValue1_set)
    screenValue2 = property(nrc_host.SafeIO_screenValue2_get, nrc_host.SafeIO_screenValue2_set)
    screenEnable = property(nrc_host.SafeIO_screenEnable_get, nrc_host.SafeIO_screenEnable_set)

    def __init__(self):
        nrc_host.SafeIO_swiginit(self, nrc_host.new_SafeIO())
    __swig_destroy__ = nrc_host.delete_SafeIO

# Register SafeIO in nrc_host:
nrc_host.SafeIO_swigregister(SafeIO)
class ModbusTCPParameter(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    IP = property(nrc_host.ModbusTCPParameter_IP_get, nrc_host.ModbusTCPParameter_IP_set)
    port = property(nrc_host.ModbusTCPParameter_port_get, nrc_host.ModbusTCPParameter_port_set)

    def __init__(self):
        nrc_host.ModbusTCPParameter_swiginit(self, nrc_host.new_ModbusTCPParameter())
    __swig_destroy__ = nrc_host.delete_ModbusTCPParameter

# Register ModbusTCPParameter in nrc_host:
nrc_host.ModbusTCPParameter_swigregister(ModbusTCPParameter)
class ModbusRTUParameter(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    slaveId = property(nrc_host.ModbusRTUParameter_slaveId_get, nrc_host.ModbusRTUParameter_slaveId_set)
    port = property(nrc_host.ModbusRTUParameter_port_get, nrc_host.ModbusRTUParameter_port_set)
    baudrate = property(nrc_host.ModbusRTUParameter_baudrate_get, nrc_host.ModbusRTUParameter_baudrate_set)
    checkBit = property(nrc_host.ModbusRTUParameter_checkBit_get, nrc_host.ModbusRTUParameter_checkBit_set)
    dataBit = property(nrc_host.ModbusRTUParameter_dataBit_get, nrc_host.ModbusRTUParameter_dataBit_set)
    stopBit = property(nrc_host.ModbusRTUParameter_stopBit_get, nrc_host.ModbusRTUParameter_stopBit_set)

    def __init__(self):
        nrc_host.ModbusRTUParameter_swiginit(self, nrc_host.new_ModbusRTUParameter())
    __swig_destroy__ = nrc_host.delete_ModbusRTUParameter

# Register ModbusRTUParameter in nrc_host:
nrc_host.ModbusRTUParameter_swigregister(ModbusRTUParameter)
class ModbusMasterParameter(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    type = property(nrc_host.ModbusMasterParameter_type_get, nrc_host.ModbusMasterParameter_type_set)
    startAddress = property(nrc_host.ModbusMasterParameter_startAddress_get, nrc_host.ModbusMasterParameter_startAddress_set)
    TCP = property(nrc_host.ModbusMasterParameter_TCP_get, nrc_host.ModbusMasterParameter_TCP_set)
    RTU = property(nrc_host.ModbusMasterParameter_RTU_get, nrc_host.ModbusMasterParameter_RTU_set)

    def __init__(self):
        nrc_host.ModbusMasterParameter_swiginit(self, nrc_host.new_ModbusMasterParameter())
    __swig_destroy__ = nrc_host.delete_ModbusMasterParameter

# Register ModbusMasterParameter in nrc_host:
nrc_host.ModbusMasterParameter_swigregister(ModbusMasterParameter)

def conveyor_belt_tracking_set_basic_parameter(socketFd, encoderVal, time, encoderDirection, encoderResolution, maxEncoderVal, minEncoderVal, posRecordMode, speed, userCoord, conveyorID, height, trackOnRunModeWithTargetOverrun, compensationEncoderVal):
    return nrc_host.conveyor_belt_tracking_set_basic_parameter(socketFd, encoderVal, time, encoderDirection, encoderResolution, maxEncoderVal, minEncoderVal, posRecordMode, speed, userCoord, conveyorID, height, trackOnRunModeWithTargetOverrun, compensationEncoderVal)

def conveyor_belt_tracking_set_basic_parameter_robot(socketFd, robotNum, encoderVal, time, encoderDirection, encoderResolution, maxEncoderVal, minEncoderVal, posRecordMode, speed, userCoord, conveyorID, height, trackOnRunModeWithTargetOverrun, compensationEncoderVal):
    return nrc_host.conveyor_belt_tracking_set_basic_parameter_robot(socketFd, robotNum, encoderVal, time, encoderDirection, encoderResolution, maxEncoderVal, minEncoderVal, posRecordMode, speed, userCoord, conveyorID, height, trackOnRunModeWithTargetOverrun, compensationEncoderVal)

def conveyor_belt_tracking_set_identification_parameter(socketFd, conveyorID, detectSrcType, capturePos, visionID, visionIoFilterType, visionLatchEncoderValueType, communication, sensorTrg, type):
    return nrc_host.conveyor_belt_tracking_set_identification_parameter(socketFd, conveyorID, detectSrcType, capturePos, visionID, visionIoFilterType, visionLatchEncoderValueType, communication, sensorTrg, type)

def conveyor_belt_tracking_set_identification_parameter_robot(socketFd, robotNum, conveyorID, detectSrcType, capturePos, visionID, visionIoFilterType, visionLatchEncoderValueType, communication, sensorTrg, type):
    return nrc_host.conveyor_belt_tracking_set_identification_parameter_robot(socketFd, robotNum, conveyorID, detectSrcType, capturePos, visionID, visionIoFilterType, visionLatchEncoderValueType, communication, sensorTrg, type)

def conveyor_belt_tracking_set_sensor_calibration(socketFd, conveyorID, sensorPos):
    return nrc_host.conveyor_belt_tracking_set_sensor_calibration(socketFd, conveyorID, sensorPos)

def conveyor_belt_tracking_set_sensor_calibration_robot(socketFd, robotNum, conveyorID, sensorPos):
    return nrc_host.conveyor_belt_tracking_set_sensor_calibration_robot(socketFd, robotNum, conveyorID, sensorPos)

def conveyor_belt_tracking_set_tracking_range(socketFd, conveyorID, receLatestPos, trackRangeXMax, trackRangeYMax, trackRangeYMin, trackRangeZMax, trackRangeZMin, trackStartXPoint):
    return nrc_host.conveyor_belt_tracking_set_tracking_range(socketFd, conveyorID, receLatestPos, trackRangeXMax, trackRangeYMax, trackRangeYMin, trackRangeZMax, trackRangeZMin, trackStartXPoint)

def conveyor_belt_tracking_set_tracking_range_robot(socketFd, robotNum, conveyorID, receLatestPos, trackRangeXMax, trackRangeYMax, trackRangeYMin, trackRangeZMax, trackRangeZMin, trackStartXPoint):
    return nrc_host.conveyor_belt_tracking_set_tracking_range_robot(socketFd, robotNum, conveyorID, receLatestPos, trackRangeXMax, trackRangeYMax, trackRangeYMin, trackRangeZMax, trackRangeZMin, trackStartXPoint)

def conveyor_belt_tracking_set_tracking_wait_point(socketFd, conveyorID, isWait, delayDetectTime, pos):
    return nrc_host.conveyor_belt_tracking_set_tracking_wait_point(socketFd, conveyorID, isWait, delayDetectTime, pos)

def conveyor_belt_tracking_set_tracking_wait_point_robot(socketFd, robotNum, conveyorID, isWait, delayDetectTime, pos):
    return nrc_host.conveyor_belt_tracking_set_tracking_wait_point_robot(socketFd, robotNum, conveyorID, isWait, delayDetectTime, pos)

def conveyor_belt_set_sensor_calibration(socketFd, conveyorID):
    return nrc_host.conveyor_belt_set_sensor_calibration(socketFd, conveyorID)

def conveyor_belt_set_sensor_calibration_robot(socketFd, robotNum, conveyorID):
    return nrc_host.conveyor_belt_set_sensor_calibration_robot(socketFd, robotNum, conveyorID)

def conveyor_belt_calibrate_for_sensor_point(socketFd, conveyorID):
    return nrc_host.conveyor_belt_calibrate_for_sensor_point(socketFd, conveyorID)

def conveyor_belt_calibrate_for_sensor_point_robot(socketFd, robotNum, conveyorID):
    return nrc_host.conveyor_belt_calibrate_for_sensor_point_robot(socketFd, robotNum, conveyorID)

def conveyor_belt_calculate_for_sensor_point(socketFd, conveyorID):
    return nrc_host.conveyor_belt_calculate_for_sensor_point(socketFd, conveyorID)

def conveyor_belt_calculate_for_sensor_point_robot(socketFd, robotNum, conveyorID):
    return nrc_host.conveyor_belt_calculate_for_sensor_point_robot(socketFd, robotNum, conveyorID)

def conveyor_belt_get_basic_paramters(socketFd, conveyorID, param):
    return nrc_host.conveyor_belt_get_basic_paramters(socketFd, conveyorID, param)

def conveyor_belt_get_basic_paramters_robot(socketFd, robotNum, conveyorID, param):
    return nrc_host.conveyor_belt_get_basic_paramters_robot(socketFd, robotNum, conveyorID, param)

def conveyor_belt_get_identification_paramters(socketFd, conveyorID, param):
    return nrc_host.conveyor_belt_get_identification_paramters(socketFd, conveyorID, param)

def conveyor_belt_get_identification_paramters_robot(socketFd, robotNum, conveyorID, param):
    return nrc_host.conveyor_belt_get_identification_paramters_robot(socketFd, robotNum, conveyorID, param)

def conveyor_belt_get_sensor_paramters(socketFd, conveyorID, param):
    return nrc_host.conveyor_belt_get_sensor_paramters(socketFd, conveyorID, param)

def conveyor_belt_get_sensor_paramters_robot(socketFd, robotNum, conveyorID, param):
    return nrc_host.conveyor_belt_get_sensor_paramters_robot(socketFd, robotNum, conveyorID, param)

def conveyor_belt_get_track_range_paramters(socketFd, conveyorID, param):
    return nrc_host.conveyor_belt_get_track_range_paramters(socketFd, conveyorID, param)

def conveyor_belt_get_track_range_paramters_robot(socketFd, robotNum, conveyorID, param):
    return nrc_host.conveyor_belt_get_track_range_paramters_robot(socketFd, robotNum, conveyorID, param)

def conveyor_belt_get_wait_point_paramters(socketFd, conveyorID, param):
    return nrc_host.conveyor_belt_get_wait_point_paramters(socketFd, conveyorID, param)

def conveyor_belt_get_wait_point_paramters_robot(socketFd, robotNum, conveyorID, param):
    return nrc_host.conveyor_belt_get_wait_point_paramters_robot(socketFd, robotNum, conveyorID, param)

def laser_cutting_set_global_parameter(socketFd, param):
    return nrc_host.laser_cutting_set_global_parameter(socketFd, param)

def laser_cutting_set_global_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_set_global_parameter_robot(socketFd, robotNum, param)

def laser_cutting_get_global_parameter(socketFd, param):
    return nrc_host.laser_cutting_get_global_parameter(socketFd, param)

def laser_cutting_get_global_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_get_global_parameter_robot(socketFd, robotNum, param)

def laser_cutting_set_craft_parameter(socketFd, param):
    return nrc_host.laser_cutting_set_craft_parameter(socketFd, param)

def laser_cutting_set_craft_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_set_craft_parameter_robot(socketFd, robotNum, param)

def laser_cutting_get_craft_parameter(socketFd, param):
    return nrc_host.laser_cutting_get_craft_parameter(socketFd, param)

def laser_cutting_get_craft_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_get_craft_parameter_robot(socketFd, robotNum, param)

def laser_cutting_set_analog_parameter(socketFd, param):
    return nrc_host.laser_cutting_set_analog_parameter(socketFd, param)

def laser_cutting_set_analog_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_set_analog_parameter_robot(socketFd, robotNum, param)

def laser_cutting_get_analog_parameter(socketFd, param):
    return nrc_host.laser_cutting_get_analog_parameter(socketFd, param)

def laser_cutting_get_analog_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_get_analog_parameter_robot(socketFd, robotNum, param)

def laser_cutting_set_io_parameter(socketFd, param):
    return nrc_host.laser_cutting_set_io_parameter(socketFd, param)

def laser_cutting_set_io_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_set_io_parameter_robot(socketFd, robotNum, param)

def laser_cutting_get_io_parameter(socketFd, param):
    return nrc_host.laser_cutting_get_io_parameter(socketFd, param)

def laser_cutting_get_io_parameter_robot(socketFd, robotNum, param):
    return nrc_host.laser_cutting_get_io_parameter_robot(socketFd, robotNum, param)

def vision_set_basic_parameter(socketFd, visionNum, vsPamrm):
    return nrc_host.vision_set_basic_parameter(socketFd, visionNum, vsPamrm)

def vision_set_basic_parameter_robot(socketFd, robotNum, visionNum, vsPamrm):
    return nrc_host.vision_set_basic_parameter_robot(socketFd, robotNum, visionNum, vsPamrm)

def vision_get_basic_parameter(socketFd, visionNum, vsPamrm):
    return nrc_host.vision_get_basic_parameter(socketFd, visionNum, vsPamrm)

def vision_get_basic_parameter_robot(socketFd, robotNum, visionNum, vsPamrm):
    return nrc_host.vision_get_basic_parameter_robot(socketFd, robotNum, visionNum, vsPamrm)

def vision_set_range(socketFd, visionNum, vsPamrm):
    return nrc_host.vision_set_range(socketFd, visionNum, vsPamrm)

def vision_set_range_robot(socketFd, robotNum, visionNum, vsPamrm):
    return nrc_host.vision_set_range_robot(socketFd, robotNum, visionNum, vsPamrm)

def vision_get_range(socketFd, visionNum, vsPamrm):
    return nrc_host.vision_get_range(socketFd, visionNum, vsPamrm)

def vision_get_range_robot(socketFd, robotNum, visionNum, vsPamrm):
    return nrc_host.vision_get_range_robot(socketFd, robotNum, visionNum, vsPamrm)

def vision_set_position_parameter(socketFd, visionNum, vsPamrm):
    return nrc_host.vision_set_position_parameter(socketFd, visionNum, vsPamrm)

def vision_set_position_parameter_robot(socketFd, robotNum, visionNum, vsPamrm):
    return nrc_host.vision_set_position_parameter_robot(socketFd, robotNum, visionNum, vsPamrm)

def vision_get_position_parameter(socketFd, visionId, vsPamrm):
    return nrc_host.vision_get_position_parameter(socketFd, visionId, vsPamrm)

def vision_get_position_parameter_robot(socketFd, robotNum, visionId, vsPamrm):
    return nrc_host.vision_get_position_parameter_robot(socketFd, robotNum, visionId, vsPamrm)

def vision_calibrate(socketFd, visionId, vsPamrm):
    return nrc_host.vision_calibrate(socketFd, visionId, vsPamrm)

def vision_calibrate_robot(socketFd, robotNum, visionId, vsPamrm):
    return nrc_host.vision_calibrate_robot(socketFd, robotNum, visionId, vsPamrm)

def vision_get_calibrate_data(socketFd, visionId, vsPamrm):
    return nrc_host.vision_get_calibrate_data(socketFd, visionId, vsPamrm)

def vision_get_calibrate_data_robot(socketFd, robotNum, visionId, vsPamrm):
    return nrc_host.vision_get_calibrate_data_robot(socketFd, robotNum, visionId, vsPamrm)

def vision_hand_eye_calibration_calculation(socketFd, visionNum):
    return nrc_host.vision_hand_eye_calibration_calculation(socketFd, visionNum)

def vision_hand_eye_calibration_calculation_robot(socketFd, robotNum, visionNum):
    return nrc_host.vision_hand_eye_calibration_calculation_robot(socketFd, robotNum, visionNum)

def weld_get_config(socketFd, index, param):
    return nrc_host.weld_get_config(socketFd, index, param)

def weld_get_config_robot(socketFd, robotNum, index, param):
    return nrc_host.weld_get_config_robot(socketFd, robotNum, index, param)

def weld_set_config(socketFd, index, param):
    return nrc_host.weld_set_config(socketFd, index, param)

def weld_set_config_robot(socketFd, robotNum, index, param):
    return nrc_host.weld_set_config_robot(socketFd, robotNum, index, param)

def weld_set_feed_wire(socketFd, state):
    return nrc_host.weld_set_feed_wire(socketFd, state)

def weld_set_feed_wire_robot(socketFd, robotNum, state):
    return nrc_host.weld_set_feed_wire_robot(socketFd, robotNum, state)

def weld_set_rewind_wire(socketFd, state):
    return nrc_host.weld_set_rewind_wire(socketFd, state)

def weld_set_rewind_wire_robot(socketFd, robotNum, state):
    return nrc_host.weld_set_rewind_wire_robot(socketFd, robotNum, state)

def weld_set_supply_gas(socketFd, state):
    return nrc_host.weld_set_supply_gas(socketFd, state)

def weld_set_supply_gas_robot(socketFd, robotNum, state):
    return nrc_host.weld_set_supply_gas_robot(socketFd, robotNum, state)

def weld_set_enable(socketFd, state):
    return nrc_host.weld_set_enable(socketFd, state)

def weld_set_enable_robot(socketFd, robotNum, state):
    return nrc_host.weld_set_enable_robot(socketFd, robotNum, state)

def weld_set_hand_spot(socketFd, state):
    return nrc_host.weld_set_hand_spot(socketFd, state)

def weld_set_hand_spot_robot(socketFd, robotNum, state):
    return nrc_host.weld_set_hand_spot_robot(socketFd, robotNum, state)

def weld_get_feed_wire_status(socketFd, status):
    return nrc_host.weld_get_feed_wire_status(socketFd, status)

def weld_get_feed_wire_status_robot(socketFd, robotNum, status):
    return nrc_host.weld_get_feed_wire_status_robot(socketFd, robotNum, status)

def weld_get_wave_weld_param(socketFd, num, param):
    return nrc_host.weld_get_wave_weld_param(socketFd, num, param)

def weld_get_wave_weld_param_robot(socketFd, robotNum, num, param):
    return nrc_host.weld_get_wave_weld_param_robot(socketFd, robotNum, num, param)

def weld_set_wave_weld_param(socketFd, num, param):
    return nrc_host.weld_set_wave_weld_param(socketFd, num, param)

def weld_set_wave_weld_param_robot(socketFd, robotNum, num, param):
    return nrc_host.weld_set_wave_weld_param_robot(socketFd, robotNum, num, param)

def weld_get_monitor_status(socketFd, status):
    return nrc_host.weld_get_monitor_status(socketFd, status)

def weld_get_monitor_status_robot(socketFd, robotNum, status):
    return nrc_host.weld_get_monitor_status_robot(socketFd, robotNum, status)

def get_library_version():
    return nrc_host.get_library_version()

def connect_robot(ip, port):
    return nrc_host.connect_robot(ip, port)

def disconnect_robot(socketFd):
    return nrc_host.disconnect_robot(socketFd)

def get_connection_status(socketFd):
    return nrc_host.get_connection_status(socketFd)

def send_message(socketFd, messageID, message):
    return nrc_host.send_message(socketFd, messageID, message)

def recv_message(*args):
    return nrc_host.recv_message(*args)

def set_receive_error_or_warnning_message_callback(*args):
    return nrc_host.set_receive_error_or_warnning_message_callback(*args)

def set_robots_parallel(socketFd, open):
    return nrc_host.set_robots_parallel(socketFd, open)

def clear_error(socketFd):
    return nrc_host.clear_error(socketFd)

def clear_error_robot(socketFd, robotNum):
    return nrc_host.clear_error_robot(socketFd, robotNum)

def set_servo_state(socketFd, state):
    return nrc_host.set_servo_state(socketFd, state)

def set_servo_state_robot(socketFd, robotNum, state):
    return nrc_host.set_servo_state_robot(socketFd, robotNum, state)

def get_servo_state(socketFd, status):
    return nrc_host.get_servo_state(socketFd, status)

def get_servo_state_robot(socketFd, robotNum, status):
    return nrc_host.get_servo_state_robot(socketFd, robotNum, status)

def set_servo_poweron(socketFd):
    return nrc_host.set_servo_poweron(socketFd)

def set_servo_poweron_robot(socketFd, robotNum):
    return nrc_host.set_servo_poweron_robot(socketFd, robotNum)

def set_servo_poweroff(socketFd):
    return nrc_host.set_servo_poweroff(socketFd)

def set_servo_poweroff_robot(socketFd, robotNum):
    return nrc_host.set_servo_poweroff_robot(socketFd, robotNum)

def get_current_position(socketFd, coord, pos):
    return nrc_host.get_current_position(socketFd, coord, pos)

def get_current_position_robot(socketFd, robotNum, coord, pos):
    return nrc_host.get_current_position_robot(socketFd, robotNum, coord, pos)

def get_current_extra_position(socketFd, pos):
    return nrc_host.get_current_extra_position(socketFd, pos)

def get_current_extra_position_robot(socketFd, robotNum, pos):
    return nrc_host.get_current_extra_position_robot(socketFd, robotNum, pos)

def get_robot_configuration(socketFd, configuration):
    return nrc_host.get_robot_configuration(socketFd, configuration)

def get_robot_configuration_robot(socketFd, robotNum, configuration):
    return nrc_host.get_robot_configuration_robot(socketFd, robotNum, configuration)

def get_robot_running_state(socketFd, status):
    return nrc_host.get_robot_running_state(socketFd, status)

def get_robot_running_state_robot(socketFd, robotNum, status):
    return nrc_host.get_robot_running_state_robot(socketFd, robotNum, status)

def set_speed(socketFd, speed):
    return nrc_host.set_speed(socketFd, speed)

def set_speed_robot(socketFd, robotNum, speed):
    return nrc_host.set_speed_robot(socketFd, robotNum, speed)

def get_speed(socketFd, speed):
    return nrc_host.get_speed(socketFd, speed)

def get_speed_robot(socketFd, robotNum, speed):
    return nrc_host.get_speed_robot(socketFd, robotNum, speed)

def set_current_coord(socketFd, coord):
    return nrc_host.set_current_coord(socketFd, coord)

def set_current_coord_robot(socketFd, robotNum, coord):
    return nrc_host.set_current_coord_robot(socketFd, robotNum, coord)

def get_current_coord(socketFd, coord):
    return nrc_host.get_current_coord(socketFd, coord)

def get_current_coord_robot(socketFd, robotNum, coord):
    return nrc_host.get_current_coord_robot(socketFd, robotNum, coord)

def set_current_mode(socketFd, mode):
    return nrc_host.set_current_mode(socketFd, mode)

def set_current_mode_robot(socketFd, robotNum, mode):
    return nrc_host.set_current_mode_robot(socketFd, robotNum, mode)

def get_current_mode(socketFd, mode):
    return nrc_host.get_current_mode(socketFd, mode)

def get_current_mode_robot(socketFd, robotNum, mode):
    return nrc_host.get_current_mode_robot(socketFd, robotNum, mode)

def get_robot_type(socketFd, type):
    return nrc_host.get_robot_type(socketFd, type)

def get_robot_type_robot(socketFd, robotNum, type):
    return nrc_host.get_robot_type_robot(socketFd, robotNum, type)

def set_teach_type(socketFd, type):
    return nrc_host.set_teach_type(socketFd, type)

def set_teach_type_robot(socketFd, robotNum, type):
    return nrc_host.set_teach_type_robot(socketFd, robotNum, type)

def get_teach_type(socketFd, type):
    return nrc_host.get_teach_type(socketFd, type)

def get_teach_type_robot(socketFd, robotNum, type):
    return nrc_host.get_teach_type_robot(socketFd, robotNum, type)

def get_pos_reachable(socketFd, pos, movetype, ret):
    return nrc_host.get_pos_reachable(socketFd, pos, movetype, ret)

def get_pos_reachable_robot(socketFd, robotNum, pos, movetype, ret):
    return nrc_host.get_pos_reachable_robot(socketFd, robotNum, pos, movetype, ret)

def set_tool_hand_number(socketFd, toolNum):
    return nrc_host.set_tool_hand_number(socketFd, toolNum)

def set_tool_hand_number_robot(socketFd, robotNum, toolNum):
    return nrc_host.set_tool_hand_number_robot(socketFd, robotNum, toolNum)

def get_tool_hand_number(socketFd, toolNum):
    return nrc_host.get_tool_hand_number(socketFd, toolNum)

def get_tool_hand_number_robot(socketFd, robotNum, toolNum):
    return nrc_host.get_tool_hand_number_robot(socketFd, robotNum, toolNum)

def set_tool_hand_param(socketFd, toolNum, param):
    return nrc_host.set_tool_hand_param(socketFd, toolNum, param)

def set_tool_hand_param_robot(socketFd, robotNum, toolNum, param):
    return nrc_host.set_tool_hand_param_robot(socketFd, robotNum, toolNum, param)

def get_tool_hand_param(socketFd, toolNum, param):
    return nrc_host.get_tool_hand_param(socketFd, toolNum, param)

def get_tool_hand_param_robot(socketFd, robotNum, toolNum, param):
    return nrc_host.get_tool_hand_param_robot(socketFd, robotNum, toolNum, param)

def tool_hand_2_or_20_point_calibrate(socketFd, point):
    return nrc_host.tool_hand_2_or_20_point_calibrate(socketFd, point)

def tool_hand_2_or_20_point_calibrate_robot(socketFd, robotNum, point):
    return nrc_host.tool_hand_2_or_20_point_calibrate_robot(socketFd, robotNum, point)

def tool_hand_2_or_20_point_calibrate_caculate(socketFd, calNum=1, noCalZero=False):
    return nrc_host.tool_hand_2_or_20_point_calibrate_caculate(socketFd, calNum, noCalZero)

def tool_hand_2_or_20_point_calibrate_caculate_robot(socketFd, robotNum, calNum=1, noCalZero=False):
    return nrc_host.tool_hand_2_or_20_point_calibrate_caculate_robot(socketFd, robotNum, calNum, noCalZero)

def tool_hand_2_or_20_point_calibrate_clear(socketFd, pointNum):
    return nrc_host.tool_hand_2_or_20_point_calibrate_clear(socketFd, pointNum)

def tool_hand_2_or_20_point_calibrate_clear_robot(socketFd, robotNum, pointNum):
    return nrc_host.tool_hand_2_or_20_point_calibrate_clear_robot(socketFd, robotNum, pointNum)

def tool_hand_7_point_calibrate(socketFd, point, toolNum):
    return nrc_host.tool_hand_7_point_calibrate(socketFd, point, toolNum)

def tool_hand_7_point_calibrate_robot(socketFd, robotNum, point, toolNum):
    return nrc_host.tool_hand_7_point_calibrate_robot(socketFd, robotNum, point, toolNum)

def tool_hand_7_point_calibrate_caculate(socketFd, toolNum, calibrationPointNum=7):
    return nrc_host.tool_hand_7_point_calibrate_caculate(socketFd, toolNum, calibrationPointNum)

def tool_hand_7_point_calibrate_caculate_robot(socketFd, robotNum, toolNum, calibrationPointNum=7):
    return nrc_host.tool_hand_7_point_calibrate_caculate_robot(socketFd, robotNum, toolNum, calibrationPointNum)

def tool_hand_7_point_calibrate_clear(socketFd, pointNum, toolNum):
    return nrc_host.tool_hand_7_point_calibrate_clear(socketFd, pointNum, toolNum)

def tool_hand_7_point_calibrate_clear_robot(socketFd, robotNum, pointNum, toolNum):
    return nrc_host.tool_hand_7_point_calibrate_clear_robot(socketFd, robotNum, pointNum, toolNum)

def set_user_coord_number(socketFd, userNum):
    return nrc_host.set_user_coord_number(socketFd, userNum)

def set_user_coord_number_robot(socketFd, robotNum, userNum):
    return nrc_host.set_user_coord_number_robot(socketFd, robotNum, userNum)

def get_user_coord_number(socketFd, userNum):
    return nrc_host.get_user_coord_number(socketFd, userNum)

def get_user_coord_number_robot(socketFd, robotNum, userNum):
    return nrc_host.get_user_coord_number_robot(socketFd, robotNum, userNum)

def get_user_coord_para(socketFd, userNum, pos):
    return nrc_host.get_user_coord_para(socketFd, userNum, pos)

def get_user_coord_para_robot(socketFd, robotNum, userNum, pos):
    return nrc_host.get_user_coord_para_robot(socketFd, robotNum, userNum, pos)

def set_user_coordinate_data(socketFd, userNum, pos):
    return nrc_host.set_user_coordinate_data(socketFd, userNum, pos)

def set_user_coordinate_data_robot(socketFd, robotNum, userNum, pos):
    return nrc_host.set_user_coordinate_data_robot(socketFd, robotNum, userNum, pos)

def calibration_oxy(socketFd, userNum, xyo):
    return nrc_host.calibration_oxy(socketFd, userNum, xyo)

def calibration_oxy_robot(socketFd, robotNum, userNum, xyo):
    return nrc_host.calibration_oxy_robot(socketFd, robotNum, userNum, xyo)

def calculate_user_coordinate(socketFd, userNumber):
    return nrc_host.calculate_user_coordinate(socketFd, userNumber)

def calculate_user_coordinate_robot(socketFd, robotNum, userNum):
    return nrc_host.calculate_user_coordinate_robot(socketFd, robotNum, userNum)

def set_global_position(socketFd, posName, posInfo):
    return nrc_host.set_global_position(socketFd, posName, posInfo)

def set_global_position_robot(socketFd, robotNum, posName, posInfo):
    return nrc_host.set_global_position_robot(socketFd, robotNum, posName, posInfo)

def get_global_position(socketFd, posName, pos):
    return nrc_host.get_global_position(socketFd, posName, pos)

def get_global_position_robot(socketFd, robotNum, posName, pos):
    return nrc_host.get_global_position_robot(socketFd, robotNum, posName, pos)

def set_global_sync_position(socketFd, posName, posInfo):
    return nrc_host.set_global_sync_position(socketFd, posName, posInfo)

def set_global_sync_position_robot(socketFd, robotNum, posName, posInfo):
    return nrc_host.set_global_sync_position_robot(socketFd, robotNum, posName, posInfo)

def get_global_sync_position(socketFd, posName, pos):
    return nrc_host.get_global_sync_position(socketFd, posName, pos)

def get_global_sync_position_robot(socketFd, robotNum, posName, pos):
    return nrc_host.get_global_sync_position_robot(socketFd, robotNum, posName, pos)

def set_global_variant(socketFd, varName, varValue):
    return nrc_host.set_global_variant(socketFd, varName, varValue)

def set_global_variant_robot(socketFd, robotNum, varName, varValue):
    return nrc_host.set_global_variant_robot(socketFd, robotNum, varName, varValue)

def get_global_variant(socketFd, varName, vaule):
    return nrc_host.get_global_variant(socketFd, varName, vaule)

def get_global_variant_robot(socketFd, robotNum, varName, vaule):
    return nrc_host.get_global_variant_robot(socketFd, robotNum, varName, vaule)

def set_axis_zero_position(socketFd, axis):
    return nrc_host.set_axis_zero_position(socketFd, axis)

def set_axis_zero_position_robot(socketFd, robotNum, axis):
    return nrc_host.set_axis_zero_position_robot(socketFd, robotNum, axis)

def set_zero_pos_deviation(socketFd, axis, shift):
    return nrc_host.set_zero_pos_deviation(socketFd, axis, shift)

def set_zero_pos_deviation_robot(socketFd, robotNum, axis, shift):
    return nrc_host.set_zero_pos_deviation_robot(socketFd, robotNum, axis, shift)

def get_single_cycle(socketFd, single_cycle):
    return nrc_host.get_single_cycle(socketFd, single_cycle)

def get_single_cycle_robot(socketFd, robotNum, single_cycle):
    return nrc_host.get_single_cycle_robot(socketFd, robotNum, single_cycle)

def get_four_point(socketFd, result):
    return nrc_host.get_four_point(socketFd, result)

def get_four_point_robot(socketFd, robotNum, result):
    return nrc_host.get_four_point_robot(socketFd, robotNum, result)

def set_four_point_mark(socketFd, point, status):
    return nrc_host.set_four_point_mark(socketFd, point, status)

def set_four_point_mark_robot(socketFd, robotNum, point, status):
    return nrc_host.set_four_point_mark_robot(socketFd, robotNum, point, status)

def four_point_calculation(socketFd, L1, L2, result):
    return nrc_host.four_point_calculation(socketFd, L1, L2, result)

def four_point_calculation_robot(socketFd, robotNum, L1, L2, result):
    return nrc_host.four_point_calculation_robot(socketFd, robotNum, L1, L2, result)

def set_result_for_DH(socketFd, apply):
    return nrc_host.set_result_for_DH(socketFd, apply)

def set_result_for_DH_robot(socketFd, robotNum, apply):
    return nrc_host.set_result_for_DH_robot(socketFd, robotNum, apply)

def get_origin_coord_to_target_coord(socketFd, originCoord, originPos, targetCoord, targetPos):
    return nrc_host.get_origin_coord_to_target_coord(socketFd, originCoord, originPos, targetCoord, targetPos)

def get_origin_coord_to_target_coord_robot(socketFd, robotNum, originCoord, originPos, targetCoord, targetPos):
    return nrc_host.get_origin_coord_to_target_coord_robot(socketFd, robotNum, originCoord, originPos, targetCoord, targetPos)

def get_robot_dh_param(socketFd, param):
    return nrc_host.get_robot_dh_param(socketFd, param)

def get_robot_dh_param_robot(socketFd, robotNum, param):
    return nrc_host.get_robot_dh_param_robot(socketFd, robotNum, param)

def get_robot_joint_param(socketFd, id, param):
    return nrc_host.get_robot_joint_param(socketFd, id, param)

def get_robot_joint_param_robot(socketFd, robotNum, id, param):
    return nrc_host.get_robot_joint_param_robot(socketFd, robotNum, id, param)

def get_teachbox_connection_status(socketFd, connected):
    return nrc_host.get_teachbox_connection_status(socketFd, connected)

def get_teachbox_connection_status_robot(socketFd, robotNum, connected):
    return nrc_host.get_teachbox_connection_status_robot(socketFd, robotNum, connected)

def get_controller_id(socketFd, id):
    return nrc_host.get_controller_id(socketFd, id)

def get_controller_id_robot(socketFd, robotNum, id):
    return nrc_host.get_controller_id_robot(socketFd, robotNum, id)

def get_controller_id_csharp(socketFd, id):
    return nrc_host.get_controller_id_csharp(socketFd, id)

def get_controller_id_csharp_robot(socketFd, robotNum, id):
    return nrc_host.get_controller_id_csharp_robot(socketFd, robotNum, id)

def get_sensor_data(socketFd, data):
    return nrc_host.get_sensor_data(socketFd, data)

def get_sensor_data_robot(socketFd, robotNum, data):
    return nrc_host.get_sensor_data_robot(socketFd, robotNum, data)

def get_static_search_position(socketFd, fileid, tableid, delaytime, pos):
    return nrc_host.get_static_search_position(socketFd, fileid, tableid, delaytime, pos)

def get_static_search_position_robot(socketFd, robotNum, fileid, tableid, delaytime, pos):
    return nrc_host.get_static_search_position_robot(socketFd, robotNum, fileid, tableid, delaytime, pos)

def get_curretn_motor_torque(socketFd, motorTorque, motorTorqueSync):
    return nrc_host.get_curretn_motor_torque(socketFd, motorTorque, motorTorqueSync)

def get_curretn_motor_torque_robot(socketFd, robotNum, motorTorque, motorTorqueSync):
    return nrc_host.get_curretn_motor_torque_robot(socketFd, robotNum, motorTorque, motorTorqueSync)

def get_curretn_motor_speed(socketFd, motorSpeed, motorSpeedSync):
    return nrc_host.get_curretn_motor_speed(socketFd, motorSpeed, motorSpeedSync)

def get_curretn_motor_speed_robot(socketFd, robotNum, motorSpeed, motorSpeedSync):
    return nrc_host.get_curretn_motor_speed_robot(socketFd, robotNum, motorSpeed, motorSpeedSync)

def get_curretn_motor_payload(socketFd, motorPayload, motorPayloadSync):
    return nrc_host.get_curretn_motor_payload(socketFd, motorPayload, motorPayloadSync)

def get_curretn_motor_payload_robot(socketFd, robotNum, motorPayload, motorPayloadSync):
    return nrc_host.get_curretn_motor_payload_robot(socketFd, robotNum, motorPayload, motorPayloadSync)

def get_curretn_line_speed_and_joint_speed(socketFd, lineSpeed, jointSpeed, jointSpeedSync):
    return nrc_host.get_curretn_line_speed_and_joint_speed(socketFd, lineSpeed, jointSpeed, jointSpeedSync)

def get_curretn_line_speed_and_joint_speed_robot(socketFd, robotNum, lineSpeed, jointSpeed, jointSpeedSync):
    return nrc_host.get_curretn_line_speed_and_joint_speed_robot(socketFd, robotNum, lineSpeed, jointSpeed, jointSpeedSync)

def robot_start_jogging(socketFd, axis, dir):
    return nrc_host.robot_start_jogging(socketFd, axis, dir)

def robot_start_jogging_robot(socketFd, robotNum, axis, dir):
    return nrc_host.robot_start_jogging_robot(socketFd, robotNum, axis, dir)

def robot_stop_jogging(socketFd, axis):
    return nrc_host.robot_stop_jogging(socketFd, axis)

def robot_stop_jogging_robot(socketFd, robotNum, axis):
    return nrc_host.robot_stop_jogging_robot(socketFd, robotNum, axis)

def robot_go_to_reset_position(socketFd):
    return nrc_host.robot_go_to_reset_position(socketFd)

def robot_go_to_reset_position_robot(socketFd, robotNum):
    return nrc_host.robot_go_to_reset_position_robot(socketFd, robotNum)

def robot_go_home(socketFd):
    return nrc_host.robot_go_home(socketFd)

def robot_go_home_robot(socketFd, robotNum):
    return nrc_host.robot_go_home_robot(socketFd, robotNum)

def robot_movej(socketFd, moveCmd):
    return nrc_host.robot_movej(socketFd, moveCmd)

def robot_movej_robot(socketFd, robotNum, moveCmd):
    return nrc_host.robot_movej_robot(socketFd, robotNum, moveCmd)

def robot_movel(socketFd, moveCmd):
    return nrc_host.robot_movel(socketFd, moveCmd)

def robot_movel_robot(socketFd, robotNum, moveCmd):
    return nrc_host.robot_movel_robot(socketFd, robotNum, moveCmd)

def robot_movec(socketFd, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_movec(socketFd, pos1, pos2, pos3, vel, coord, acc, dec)

def robot_movec_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_movec_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec)

def robot_moveca(socketFd, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_moveca(socketFd, pos1, pos2, pos3, vel, coord, acc, dec)

def robot_moveca_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_moveca_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec)

def robot_moves(socketFd, pos, vel, coord, acc, dec):
    return nrc_host.robot_moves(socketFd, pos, vel, coord, acc, dec)

def robot_moves_robot(socketFd, robotNum, pos, vel, coord, acc, dec):
    return nrc_host.robot_moves_robot(socketFd, robotNum, pos, vel, coord, acc, dec)

def robot_extra_movej(socketFd, moveCmd):
    return nrc_host.robot_extra_movej(socketFd, moveCmd)

def robot_extra_movej_robot(socketFd, robotNum, moveCmd):
    return nrc_host.robot_extra_movej_robot(socketFd, robotNum, moveCmd)

def robot_extra_movel(socketFd, moveCmd):
    return nrc_host.robot_extra_movel(socketFd, moveCmd)

def robot_extra_movel_robot(socketFd, robotNum, moveCmd):
    return nrc_host.robot_extra_movel_robot(socketFd, robotNum, moveCmd)

def robot_extra_movec(socketFd, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_extra_movec(socketFd, pos1, pos2, pos3, vel, coord, acc, dec)

def robot_extra_movec_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec):
    return nrc_host.robot_extra_movec_robot(socketFd, robotNum, pos1, pos2, pos3, vel, coord, acc, dec)

def servo_move(socketFd, servoMove):
    return nrc_host.servo_move(socketFd, servoMove)

def servo_move_robot(socketFd, robotNum, servoMove):
    return nrc_host.servo_move_robot(socketFd, robotNum, servoMove)

def set_digital_output(socketFd, port, value):
    return nrc_host.set_digital_output(socketFd, port, value)

def get_digital_output(socketFd, out):
    return nrc_host.get_digital_output(socketFd, out)

def get_digital_input(socketFd, _in):
    return nrc_host.get_digital_input(socketFd, _in)

def set_analog_output(socketFd, port, value):
    return nrc_host.set_analog_output(socketFd, port, value)

def get_analog_output(socketFd, aout):
    return nrc_host.get_analog_output(socketFd, aout)

def get_analog_input(socketFd, ain):
    return nrc_host.get_analog_input(socketFd, ain)

def set_force_digital_input(socketFd, port, force, value):
    return nrc_host.set_force_digital_input(socketFd, port, force, value)

def get_force_digital_input(socketFd, port, status):
    return nrc_host.get_force_digital_input(socketFd, port, status)

def set_IO_reset_function(socketFd, robotNum, type, enable, value):
    return nrc_host.set_IO_reset_function(socketFd, robotNum, type, enable, value)

def get_IO_reset_function(socketFd, robotNum, type, enable, value):
    return nrc_host.get_IO_reset_function(socketFd, robotNum, type, enable, value)

def set_error_msg_of_digital_input(socketFd, msg):
    return nrc_host.set_error_msg_of_digital_input(socketFd, msg)

def get_error_msg_of_digital_input(socketFd, msg):
    return nrc_host.get_error_msg_of_digital_input(socketFd, msg)

def set_error_msg_of_digital_output(socketFd, msg):
    return nrc_host.set_error_msg_of_digital_output(socketFd, msg)

def get_error_msg_of_digital_output(socketFd, msg):
    return nrc_host.get_error_msg_of_digital_output(socketFd, msg)

def set_remote_param(socketFd, robotNum, speed, start, time, startTime, num=10):
    return nrc_host.set_remote_param(socketFd, robotNum, speed, start, time, startTime, num)

def get_remote_param(socketFd, robotNum, speed, start, time, startTime, num):
    return nrc_host.get_remote_param(socketFd, robotNum, speed, start, time, startTime, num)

def set_remote_function(socketFd, robotNum, general, program, num=10):
    return nrc_host.set_remote_function(socketFd, robotNum, general, program, num)

def get_remote_function(socketFd, robotNum, num, time, general, program):
    return nrc_host.get_remote_function(socketFd, robotNum, num, time, general, program)

def set_remote_status_tips(socketFd, robotNum, outagePort, outageValue, program):
    return nrc_host.set_remote_status_tips(socketFd, robotNum, outagePort, outageValue, program)

def get_remote_status_tips(socketFd, robotNum, num, outagePort, outageValue, program):
    return nrc_host.get_remote_status_tips(socketFd, robotNum, num, outagePort, outageValue, program)

def set_remote_program(socketFd, robotNum, program):
    return nrc_host.set_remote_program(socketFd, robotNum, program)

def get_remote_program(socketFd, robotNum, num, program):
    return nrc_host.get_remote_program(socketFd, robotNum, num, program)

def set_hard_enable_port(socketFd, enable, port1, port2):
    return nrc_host.set_hard_enable_port(socketFd, enable, port1, port2)

def get_hard_enable_port(socketFd, enable, port1, port2):
    return nrc_host.get_hard_enable_port(socketFd, enable, port1, port2)

def set_safe_IO_function(socketFd, robotNum, safeIO):
    return nrc_host.set_safe_IO_function(socketFd, robotNum, safeIO)

def get_safe_IO_function(socketFd, robotNum, safeIO):
    return nrc_host.get_safe_IO_function(socketFd, robotNum, safeIO)

def job_upload_by_directory(socketFd, directoryPath):
    return nrc_host.job_upload_by_directory(socketFd, directoryPath)

def job_upload_by_file(socketFd, filePath):
    return nrc_host.job_upload_by_file(socketFd, filePath)

def job_sync_job_file(socketFd):
    return nrc_host.job_sync_job_file(socketFd)

def job_download_by_directory(socketFd, directoryPath, isCover):
    return nrc_host.job_download_by_directory(socketFd, directoryPath, isCover)

def log_download_by_quantity(socketFd, counts, directoryPath):
    return nrc_host.log_download_by_quantity(socketFd, counts, directoryPath)

def backup_system(socketFd):
    return nrc_host.backup_system(socketFd)

def job_create(socketFd, jobName):
    return nrc_host.job_create(socketFd, jobName)

def job_create_robot(socketFd, robotNum, jobName):
    return nrc_host.job_create_robot(socketFd, robotNum, jobName)

def job_delete(socketFd, jobName):
    return nrc_host.job_delete(socketFd, jobName)

def job_delete_robot(socketFd, robotNum, jobName):
    return nrc_host.job_delete_robot(socketFd, robotNum, jobName)

def job_open(socketFd, jobName):
    return nrc_host.job_open(socketFd, jobName)

def job_open_robot(socketFd, robotNum, jobName):
    return nrc_host.job_open_robot(socketFd, robotNum, jobName)

def job_get_command_total_lines(socketFd, totalLines):
    return nrc_host.job_get_command_total_lines(socketFd, totalLines)

def job_get_command_total_lines_robot(socketFd, robotNum, totalLines):
    return nrc_host.job_get_command_total_lines_robot(socketFd, robotNum, totalLines)

def job_get_command_content_by_line(socketFd, line, commandType, jobContent):
    return nrc_host.job_get_command_content_by_line(socketFd, line, commandType, jobContent)

def job_get_command_content_by_line_robot(socketFd, robotNum, line, commandType, jobContent):
    return nrc_host.job_get_command_content_by_line_robot(socketFd, robotNum, line, commandType, jobContent)

def job_delete_command_by_line(socketFd, line):
    return nrc_host.job_delete_command_by_line(socketFd, line)

def job_delete_command_by_line_robot(socketFd, robotNum, line):
    return nrc_host.job_delete_command_by_line_robot(socketFd, robotNum, line)

def job_run(socketFd, jobName):
    return nrc_host.job_run(socketFd, jobName)

def job_run_robot(socketFd, robotNum, jobName):
    return nrc_host.job_run_robot(socketFd, robotNum, jobName)

def job_step(socketFd, jobName, line):
    return nrc_host.job_step(socketFd, jobName, line)

def job_step_robot(socketFd, robotNum, jobName, line):
    return nrc_host.job_step_robot(socketFd, robotNum, jobName, line)

def job_pause(socketFd):
    return nrc_host.job_pause(socketFd)

def job_pause_robot(socketFd, robotNum):
    return nrc_host.job_pause_robot(socketFd, robotNum)

def job_continue(socketFd):
    return nrc_host.job_continue(socketFd)

def job_continue_robot(socketFd, robotNum):
    return nrc_host.job_continue_robot(socketFd, robotNum)

def job_stop(socketFd):
    return nrc_host.job_stop(socketFd)

def job_stop_robot(socketFd, robotNum):
    return nrc_host.job_stop_robot(socketFd, robotNum)

def job_run_times(socketFd, index):
    return nrc_host.job_run_times(socketFd, index)

def job_run_times_robot(socketFd, robotNum, index):
    return nrc_host.job_run_times_robot(socketFd, robotNum, index)

def job_break_point_run(socketFd, jobName):
    return nrc_host.job_break_point_run(socketFd, jobName)

def job_break_point_run_robot(socketFd, robotNum, jobName):
    return nrc_host.job_break_point_run_robot(socketFd, robotNum, jobName)

def job_get_current_file(socketFd, jobName):
    return nrc_host.job_get_current_file(socketFd, jobName)

def job_get_current_file_robot(socketFd, robotNum, jobName):
    return nrc_host.job_get_current_file_robot(socketFd, robotNum, jobName)

def job_get_current_file_csharp(socketFd, jobName):
    return nrc_host.job_get_current_file_csharp(socketFd, jobName)

def job_get_current_file_csharp_robot(socketFd, robotNum, jobName):
    return nrc_host.job_get_current_file_csharp_robot(socketFd, robotNum, jobName)

def job_get_current_line(socketFd, line):
    return nrc_host.job_get_current_line(socketFd, line)

def job_get_current_line_robot(socketFd, robotNum, line):
    return nrc_host.job_get_current_line_robot(socketFd, robotNum, line)

def job_insert_local_position(socketFd, posData):
    return nrc_host.job_insert_local_position(socketFd, posData)

def job_insert_local_position_robot(socketFd, robotNum, posData):
    return nrc_host.job_insert_local_position_robot(socketFd, robotNum, posData)

def job_set_local_position(socketFd, posName, posInfo):
    return nrc_host.job_set_local_position(socketFd, posName, posInfo)

def job_set_local_position_robot(socketFd, robotNum, posName, posInfo):
    return nrc_host.job_set_local_position_robot(socketFd, robotNum, posName, posInfo)

def job_insert_moveJ(socketFd, line, moveCmd):
    return nrc_host.job_insert_moveJ(socketFd, line, moveCmd)

def job_insert_moveJ_robot(socketFd, robotNum, line, moveCmd):
    return nrc_host.job_insert_moveJ_robot(socketFd, robotNum, line, moveCmd)

def job_insert_moveL(socketFd, line, moveCmd):
    return nrc_host.job_insert_moveL(socketFd, line, moveCmd)

def job_insert_moveL_robot(socketFd, robotNum, line, moveCmd):
    return nrc_host.job_insert_moveL_robot(socketFd, robotNum, line, moveCmd)

def job_insert_moveC(socketFd, line, moveCmd):
    return nrc_host.job_insert_moveC(socketFd, line, moveCmd)

def job_insert_moveC_robot(socketFd, robotNum, line, moveCmd):
    return nrc_host.job_insert_moveC_robot(socketFd, robotNum, line, moveCmd)

def job_insert_imove(socketFd, line, moveCmd):
    return nrc_host.job_insert_imove(socketFd, line, moveCmd)

def job_insert_imove_rbobt(socketFd, robotNum, line, moveCmd):
    return nrc_host.job_insert_imove_rbobt(socketFd, robotNum, line, moveCmd)

def job_insert_moveComm(socketFd, line, moveType, m_vel, m_acc, m_dec, m_time, m_pl):
    return nrc_host.job_insert_moveComm(socketFd, line, moveType, m_vel, m_acc, m_dec, m_time, m_pl)

def job_insert_moveComm_rbobt(socketFd, robotNum, line, moveType, m_vel, m_acc, m_dec, m_time, m_pl):
    return nrc_host.job_insert_moveComm_rbobt(socketFd, robotNum, line, moveType, m_vel, m_acc, m_dec, m_time, m_pl)

def job_insert_samov_command(socketFd, line, moveCmd, posData):
    return nrc_host.job_insert_samov_command(socketFd, line, moveCmd, posData)

def job_insert_samov_command_robot(socketFd, robotNum, line, moveCmd, posData):
    return nrc_host.job_insert_samov_command_robot(socketFd, robotNum, line, moveCmd, posData)

def job_insert_timer_command(socketFd, line, time):
    return nrc_host.job_insert_timer_command(socketFd, line, time)

def job_insert_timer_command_robot(socketFd, robotNum, line, time):
    return nrc_host.job_insert_timer_command_robot(socketFd, robotNum, line, time)

def job_insert_io_out_command(socketFd, line, params):
    return nrc_host.job_insert_io_out_command(socketFd, line, params)

def job_insert_io_out_command_robot(socketFd, robotNum, line, params):
    return nrc_host.job_insert_io_out_command_robot(socketFd, robotNum, line, params)

def job_insert_until(socketFd, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_until(socketFd, line, conditionGroups, logic, logicGroup)

def job_insert_until_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_until_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup)

def job_insert_end_until(socketFd, line):
    return nrc_host.job_insert_end_until(socketFd, line)

def job_insert_end_until_robot(socketFd, robotNum, line):
    return nrc_host.job_insert_end_until_robot(socketFd, robotNum, line)

def job_insert_while(socketFd, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_while(socketFd, line, conditionGroups, logic, logicGroup)

def job_insert_while_robot(socketFd, rbobtNum, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_while_robot(socketFd, rbobtNum, line, conditionGroups, logic, logicGroup)

def job_insert_end_while(socketFd, line):
    return nrc_host.job_insert_end_while(socketFd, line)

def job_insert_end_while_robot(socketFd, rbobtNum, line):
    return nrc_host.job_insert_end_while_robot(socketFd, rbobtNum, line)

def job_insert_if(socketFd, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_if(socketFd, line, conditionGroups, logic, logicGroup)

def job_insert_if_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup):
    return nrc_host.job_insert_if_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup)

def job_insert_end_if(socketFd, line):
    return nrc_host.job_insert_end_if(socketFd, line)

def job_insert_end_if_robot(socketFd, robotNum, line):
    return nrc_host.job_insert_end_if_robot(socketFd, robotNum, line)

def job_insert_label(socketFd, line, label):
    return nrc_host.job_insert_label(socketFd, line, label)

def job_insert_label_robot(socketFd, robotNum, line, label):
    return nrc_host.job_insert_label_robot(socketFd, robotNum, line, label)

def job_insert_jump(socketFd, line, conditionGroups, logic, logicGroup, jumpConditionFlag, label):
    return nrc_host.job_insert_jump(socketFd, line, conditionGroups, logic, logicGroup, jumpConditionFlag, label)

def job_insert_jump_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup, jumpConditionFlag, label):
    return nrc_host.job_insert_jump_robot(socketFd, robotNum, line, conditionGroups, logic, logicGroup, jumpConditionFlag, label)

def job_insert_vision_craft_start(socketFd, line, id):
    return nrc_host.job_insert_vision_craft_start(socketFd, line, id)

def job_insert_vision_craft_start_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_vision_craft_start_robot(socketFd, robotNum, line, id)

def job_insert_vision_craft_get_pos(socketFd, line, id, posName):
    return nrc_host.job_insert_vision_craft_get_pos(socketFd, line, id, posName)

def job_insert_vision_craft_get_pos_robot(socketFd, robotNum, line, id, posName):
    return nrc_host.job_insert_vision_craft_get_pos_robot(socketFd, robotNum, line, id, posName)

def job_insert_vision_craft_visual_trigger(socketFd, line, id):
    return nrc_host.job_insert_vision_craft_visual_trigger(socketFd, line, id)

def job_insert_vision_craft_visual_trigger_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_vision_craft_visual_trigger_robot(socketFd, robotNum, line, id)

def job_insert_vision_craft_visual_end(socketFd, line, id):
    return nrc_host.job_insert_vision_craft_visual_end(socketFd, line, id)

def job_insert_vision_craft_visual_end_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_vision_craft_visual_end_robot(socketFd, robotNum, line, id)

def job_insert_conveyor_check_pos(socketFd, line, id):
    return nrc_host.job_insert_conveyor_check_pos(socketFd, line, id)

def job_insert_conveyor_check_pos_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_conveyor_check_pos_robot(socketFd, robotNum, line, id)

def job_insert_conveyor_check_end(socketFd, line, id):
    return nrc_host.job_insert_conveyor_check_end(socketFd, line, id)

def job_insert_conveyor_check_end_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_conveyor_check_end_robot(socketFd, robotNum, line, id)

def job_insert_conveyor_on(socketFd, line, id, postype, pos, vel, acc):
    return nrc_host.job_insert_conveyor_on(socketFd, line, id, postype, pos, vel, acc)

def job_insert_conveyor_on_robot(socketFd, robotNum, line, id, postype, pos, vel, acc):
    return nrc_host.job_insert_conveyor_on_robot(socketFd, robotNum, line, id, postype, pos, vel, acc)

def job_insert_conveyor_off(socketFd, line, id):
    return nrc_host.job_insert_conveyor_off(socketFd, line, id)

def job_insert_conveyor_off_robot(socketFd, robotNum, line, id):
    return nrc_host.job_insert_conveyor_off_robot(socketFd, robotNum, line, id)

def job_insert_conveyor_pos(socketFd, line, id, posName):
    return nrc_host.job_insert_conveyor_pos(socketFd, line, id, posName)

def job_insert_conveyor_pos_robot(socketFd, robotNum, line, id, posName):
    return nrc_host.job_insert_conveyor_pos_robot(socketFd, robotNum, line, id, posName)

def job_insert_conveyor_clear(socketFd, line, id, removeType):
    return nrc_host.job_insert_conveyor_clear(socketFd, line, id, removeType)

def job_insert_conveyor_clear_robot(socketFd, robotNum, line, id, removeType):
    return nrc_host.job_insert_conveyor_clear_robot(socketFd, robotNum, line, id, removeType)

def job_insert_cil(socketFd, line, moveCmd, id):
    return nrc_host.job_insert_cil(socketFd, line, moveCmd, id)

def job_insert_cil_robot(socketFd, robotNum, line, moveCmd, id):
    return nrc_host.job_insert_cil_robot(socketFd, robotNum, line, moveCmd, id)

def modbus_set_master_parameter(socketFd, id, param):
    return nrc_host.modbus_set_master_parameter(socketFd, id, param)

def modbus_open_master(socketFd, id):
    return nrc_host.modbus_open_master(socketFd, id)

def modbus_get_master_connection_status(socketFd, id, status):
    return nrc_host.modbus_get_master_connection_status(socketFd, id, status)

def modbus_read_coil_status(socketFd, id, address, quantity, data):
    return nrc_host.modbus_read_coil_status(socketFd, id, address, quantity, data)

def modbus_read_input_status(socketFd, id, address, quantity, data):
    return nrc_host.modbus_read_input_status(socketFd, id, address, quantity, data)

def modbus_read_holding_registers(socketFd, id, address, quantity, data):
    return nrc_host.modbus_read_holding_registers(socketFd, id, address, quantity, data)

def modbus_read_input_registers(socketFd, id, address, quantity, data):
    return nrc_host.modbus_read_input_registers(socketFd, id, address, quantity, data)

def modbus_write_signal_coil_status(socketFd, id, address, data):
    return nrc_host.modbus_write_signal_coil_status(socketFd, id, address, data)

def modbus_write_signal_holding_registers(socketFd, id, address, data):
    return nrc_host.modbus_write_signal_holding_registers(socketFd, id, address, data)

def modbus_write_multiple_coil_status(socketFd, id, address, data):
    return nrc_host.modbus_write_multiple_coil_status(socketFd, id, address, data)

def modbus_write_multiple_holding_registers(socketFd, id, address, data):
    return nrc_host.modbus_write_multiple_holding_registers(socketFd, id, address, data)

def queue_motion_set_status(socketFd, status):
    return nrc_host.queue_motion_set_status(socketFd, status)

def queue_motion_set_status_robot(socketFd, robotNum, status):
    return nrc_host.queue_motion_set_status_robot(socketFd, robotNum, status)

def queue_motion_get_status(socketFd, status):
    return nrc_host.queue_motion_get_status(socketFd, status)

def queue_motion_get_status_robot(socketFd, robotNum, status):
    return nrc_host.queue_motion_get_status_robot(socketFd, robotNum, status)

def queue_motion_clear_Data(socketFd):
    return nrc_host.queue_motion_clear_Data(socketFd)

def queue_motion_clear_Data_robot(socketFd, robotNum):
    return nrc_host.queue_motion_clear_Data_robot(socketFd, robotNum)

def queue_motion_size(socketFd, size):
    return nrc_host.queue_motion_size(socketFd, size)

def queue_motion_size_robot(socketFd, robotNum, size):
    return nrc_host.queue_motion_size_robot(socketFd, robotNum, size)

def queue_motion_get_queuelen(socketFd, len):
    return nrc_host.queue_motion_get_queuelen(socketFd, len)

def queue_motion_get_queuelen_robot(socketFd, robotNum, len):
    return nrc_host.queue_motion_get_queuelen_robot(socketFd, robotNum, len)

def queue_motion_send_to_controller(socketFd, size):
    return nrc_host.queue_motion_send_to_controller(socketFd, size)

def queue_motion_send_to_controller_robot(socketFd, robotNum, size):
    return nrc_host.queue_motion_send_to_controller_robot(socketFd, robotNum, size)

def queue_motion_suspend(socketFd):
    return nrc_host.queue_motion_suspend(socketFd)

def queue_motion_suspend_robot(socketFd, robotNum):
    return nrc_host.queue_motion_suspend_robot(socketFd, robotNum)

def queue_motion_restart(socketFd):
    return nrc_host.queue_motion_restart(socketFd)

def queue_motion_restart_robot(socketFd, robotNum):
    return nrc_host.queue_motion_restart_robot(socketFd, robotNum)

def queue_motion_stop(socketFd):
    return nrc_host.queue_motion_stop(socketFd)

def queue_motion_stop_robot(socketFd, robotNum):
    return nrc_host.queue_motion_stop_robot(socketFd, robotNum)

def queue_motion_stop_not_power_off(socketFd):
    return nrc_host.queue_motion_stop_not_power_off(socketFd)

def queue_motion_stop_not_power_off_robot(socketFd, robotNum):
    return nrc_host.queue_motion_stop_not_power_off_robot(socketFd, robotNum)

def queue_motion_push_back_moveJ(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveJ(socketFd, moveCmd)

def queue_motion_push_back_moveJ_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveJ_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveL(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveL(socketFd, moveCmd)

def queue_motion_push_back_moveL_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveL_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveC(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveC(socketFd, moveCmd)

def queue_motion_push_back_moveC_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveC_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveCA(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveCA(socketFd, moveCmd)

def queue_motion_push_back_moveCA_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveCA_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveS(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveS(socketFd, moveCmd)

def queue_motion_push_back_moveS_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveS_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveJ_extra(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveJ_extra(socketFd, moveCmd)

def queue_motion_push_back_moveJ_extra_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveJ_extra_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_moveL_extra(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_moveL_extra(socketFd, moveCmd)

def queue_motion_push_back_moveL_extra_robot(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_moveL_extra_robot(socketFd, robotNum, moveCmd)

def queue_motion_push_back_imove(socketFd, moveCmd):
    return nrc_host.queue_motion_push_back_imove(socketFd, moveCmd)

def queue_motion_push_back_imove_rbobt(socketFd, robotNum, moveCmd):
    return nrc_host.queue_motion_push_back_imove_rbobt(socketFd, robotNum, moveCmd)

def queue_motion_push_back_timer(socketFd, time):
    return nrc_host.queue_motion_push_back_timer(socketFd, time)

def queue_motion_push_back_timer_robot(socketFd, robotNum, time):
    return nrc_host.queue_motion_push_back_timer_robot(socketFd, robotNum, time)

def queue_motion_push_back_dout(socketFd, port, value):
    return nrc_host.queue_motion_push_back_dout(socketFd, port, value)

def queue_motion_push_back_dout_robot(socketFd, robotNum, port, value):
    return nrc_host.queue_motion_push_back_dout_robot(socketFd, robotNum, port, value)

def queue_motion_push_back_conveyor_check_pos(socketFd, id):
    return nrc_host.queue_motion_push_back_conveyor_check_pos(socketFd, id)

def queue_motion_push_back_conveyor_check_pos_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_conveyor_check_pos_robot(socketFd, robotNum, id)

def queue_motion_push_back_conveyor_check_end(socketFd, id):
    return nrc_host.queue_motion_push_back_conveyor_check_end(socketFd, id)

def queue_motion_push_back_conveyor_check_end_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_conveyor_check_end_robot(socketFd, robotNum, id)

def queue_motion_push_back_conveyor_on(socketFd, id, postype, pos, vel, acc):
    return nrc_host.queue_motion_push_back_conveyor_on(socketFd, id, postype, pos, vel, acc)

def queue_motion_push_back_conveyor_on_robot(socketFd, robotNum, id, postype, pos, vel, acc):
    return nrc_host.queue_motion_push_back_conveyor_on_robot(socketFd, robotNum, id, postype, pos, vel, acc)

def queue_motion_push_back_conveyor_off(socketFd, id):
    return nrc_host.queue_motion_push_back_conveyor_off(socketFd, id)

def queue_motion_push_back_conveyor_off_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_conveyor_off_robot(socketFd, robotNum, id)

def queue_motion_push_back_conveyor_pos(socketFd, id, posName):
    return nrc_host.queue_motion_push_back_conveyor_pos(socketFd, id, posName)

def queue_motion_push_back_conveyor_pos_robot(socketFd, robotNum, id, posName):
    return nrc_host.queue_motion_push_back_conveyor_pos_robot(socketFd, robotNum, id, posName)

def queue_motion_push_back_conveyor_clear(socketFd, id, removeType):
    return nrc_host.queue_motion_push_back_conveyor_clear(socketFd, id, removeType)

def queue_motion_push_back_conveyor_clear_robot(socketFd, robotNum, id, removeType):
    return nrc_host.queue_motion_push_back_conveyor_clear_robot(socketFd, robotNum, id, removeType)

def queue_motion_push_back_arc_on(socketFd, id):
    return nrc_host.queue_motion_push_back_arc_on(socketFd, id)

def queue_motion_push_back_arc_on_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_arc_on_robot(socketFd, robotNum, id)

def queue_motion_push_back_arc_off(socketFd, id):
    return nrc_host.queue_motion_push_back_arc_off(socketFd, id)

def queue_motion_push_back_arc_off_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_arc_off_robot(socketFd, robotNum, id)

def queue_motion_push_back_wave_on(socketFd, id):
    return nrc_host.queue_motion_push_back_wave_on(socketFd, id)

def queue_motion_push_back_wave_on_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_wave_on_robot(socketFd, robotNum, id)

def queue_motion_push_back_wave_off(socketFd, id):
    return nrc_host.queue_motion_push_back_wave_off(socketFd, id)

def queue_motion_push_back_wave_off_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_wave_off_robot(socketFd, robotNum, id)

def queue_motion_push_back_tigweld_on(socketFd, type, l1, l2):
    return nrc_host.queue_motion_push_back_tigweld_on(socketFd, type, l1, l2)

def queue_motion_push_back_tigweld_on_robot(socketFd, robotNum, type, l1, l2):
    return nrc_host.queue_motion_push_back_tigweld_on_robot(socketFd, robotNum, type, l1, l2)

def queue_motion_push_back_tigweld_off(socketFd):
    return nrc_host.queue_motion_push_back_tigweld_off(socketFd)

def queue_motion_push_back_tigweld_off_robot(socketFd, robotNum):
    return nrc_host.queue_motion_push_back_tigweld_off_robot(socketFd, robotNum)

def queue_motion_push_back_spot_weld(socketFd, id, time):
    return nrc_host.queue_motion_push_back_spot_weld(socketFd, id, time)

def queue_motion_push_back_spot_weld_robot(socketFd, robotNum, id, time):
    return nrc_host.queue_motion_push_back_spot_weld_robot(socketFd, robotNum, id, time)

def queue_motion_push_back_vision_craft_start(socketFd, id):
    return nrc_host.queue_motion_push_back_vision_craft_start(socketFd, id)

def queue_motion_push_back_vision_craft_start_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_vision_craft_start_robot(socketFd, robotNum, id)

def queue_motion_push_back_vision_craft_get_pos(socketFd, id, posName):
    return nrc_host.queue_motion_push_back_vision_craft_get_pos(socketFd, id, posName)

def queue_motion_push_back_vision_craft_get_pos_robot(socketFd, robotNum, id, posName):
    return nrc_host.queue_motion_push_back_vision_craft_get_pos_robot(socketFd, robotNum, id, posName)

def queue_motion_push_back_vision_craft_visual_trigger(socketFd, id):
    return nrc_host.queue_motion_push_back_vision_craft_visual_trigger(socketFd, id)

def queue_motion_push_back_vision_craft_visual_trigger_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_vision_craft_visual_trigger_robot(socketFd, robotNum, id)

def queue_motion_push_back_vision_craft_visual_end(socketFd, id):
    return nrc_host.queue_motion_push_back_vision_craft_visual_end(socketFd, id)

def queue_motion_push_back_vision_craft_visual_end_robot(socketFd, robotNum, id):
    return nrc_host.queue_motion_push_back_vision_craft_visual_end_robot(socketFd, robotNum, id)

def queue_motion_push_back_until(socketFd, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_until(socketFd, conditionGroups, logic, logicGroup)

def queue_motion_push_back_until_robot(socketFd, robotNum, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_until_robot(socketFd, robotNum, conditionGroups, logic, logicGroup)

def queue_motion_push_back_end_until(socketFd):
    return nrc_host.queue_motion_push_back_end_until(socketFd)

def queue_motion_push_back_end_until_robot(socketFd, robotNum):
    return nrc_host.queue_motion_push_back_end_until_robot(socketFd, robotNum)

def queue_motion_push_back_while(socketFd, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_while(socketFd, conditionGroups, logic, logicGroup)

def queue_motion_push_back_while_robot(socketFd, robotNum, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_while_robot(socketFd, robotNum, conditionGroups, logic, logicGroup)

def queue_motion_push_back_end_while(socketFd):
    return nrc_host.queue_motion_push_back_end_while(socketFd)

def queue_motion_push_back_end_while_robot(socketFd, robotNum):
    return nrc_host.queue_motion_push_back_end_while_robot(socketFd, robotNum)

def queue_motion_push_back_if(socketFd, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_if(socketFd, conditionGroups, logic, logicGroup)

def queue_motion_push_back_if_robot(socketFd, robotNum, conditionGroups, logic, logicGroup):
    return nrc_host.queue_motion_push_back_if_robot(socketFd, robotNum, conditionGroups, logic, logicGroup)

def queue_motion_push_back_end_if(socketFd):
    return nrc_host.queue_motion_push_back_end_if(socketFd)

def queue_motion_push_back_end_if_robot(socketFd, robotNum):
    return nrc_host.queue_motion_push_back_end_if_robot(socketFd, robotNum)

def queue_motion_push_back_label(socketFd, label):
    return nrc_host.queue_motion_push_back_label(socketFd, label)

def queue_motion_push_back_label_robot(socketFd, robotNum, label):
    return nrc_host.queue_motion_push_back_label_robot(socketFd, robotNum, label)

def queue_motion_push_back_jump(socketFd, conditionGroups, logic, logicGroup, jumpConditionFlag, label):
    return nrc_host.queue_motion_push_back_jump(socketFd, conditionGroups, logic, logicGroup, jumpConditionFlag, label)

def queue_motion_push_back_jump_robot(socketFd, robotNum, conditionGroups, logic, logicGroup, jumpConditionFlag, label):
    return nrc_host.queue_motion_push_back_jump_robot(socketFd, robotNum, conditionGroups, logic, logicGroup, jumpConditionFlag, label)

def queue_motion_push_back_samov(socketFd, moveCmd, posData):
    return nrc_host.queue_motion_push_back_samov(socketFd, moveCmd, posData)

def queue_motion_push_back_samov_robot(socketFd, robotNum, moveCmd, posData):
    return nrc_host.queue_motion_push_back_samov_robot(socketFd, robotNum, moveCmd, posData)

def queue_motion_push_back_cil(socketFd, moveCmd, id):
    return nrc_host.queue_motion_push_back_cil(socketFd, moveCmd, id)

def queue_motion_push_back_cil_robot(socketFd, robotNum, moveCmd, id):
    return nrc_host.queue_motion_push_back_cil_robot(socketFd, robotNum, moveCmd, id)

def queue_motion_push_back_TOFFSETON(socketFd, params):
    return nrc_host.queue_motion_push_back_TOFFSETON(socketFd, params)

def queue_motion_push_back_TOFFSETON_robot(socketFd, robotNum, params):
    return nrc_host.queue_motion_push_back_TOFFSETON_robot(socketFd, robotNum, params)

def queue_motion_push_back_TOFFSETOFF(socketFd):
    return nrc_host.queue_motion_push_back_TOFFSETOFF(socketFd)

def queue_motion_push_back_TOFFSETOFF_robot(socketFd, robotNum):
    return nrc_host.queue_motion_push_back_TOFFSETOFF_robot(socketFd, robotNum)

def track_record_start(socketFd, maxSamplingNum, samplingInterval):
    return nrc_host.track_record_start(socketFd, maxSamplingNum, samplingInterval)

def track_record_start_robot(socketFd, robotNum, maxSamplingNum, samplingInterval):
    return nrc_host.track_record_start_robot(socketFd, robotNum, maxSamplingNum, samplingInterval)

def track_record_stop(socketFd):
    return nrc_host.track_record_stop(socketFd)

def track_record_stop_robot(socketFd, robotNum):
    return nrc_host.track_record_stop_robot(socketFd, robotNum)

def get_track_record_status(socketFd, recordStart):
    return nrc_host.get_track_record_status(socketFd, recordStart)

def get_track_record_status_robot(socketFd, robotNum, recordStart):
    return nrc_host.get_track_record_status_robot(socketFd, robotNum, recordStart)

def track_record_save(socketFd, trajName):
    return nrc_host.track_record_save(socketFd, trajName)

def track_record_save_robot(socketFd, robotNum, trajName):
    return nrc_host.track_record_save_robot(socketFd, robotNum, trajName)

def track_record_playback(socketFd, vel):
    return nrc_host.track_record_playback(socketFd, vel)

def track_record_playback_robot(socketFd, robotNum, vel):
    return nrc_host.track_record_playback_robot(socketFd, robotNum, vel)

def track_record_delete(socketFd):
    return nrc_host.track_record_delete(socketFd)

def track_record_delete_robot(socketFd, robotNum):
    return nrc_host.track_record_delete_robot(socketFd, robotNum)

