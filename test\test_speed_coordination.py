#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试位置变化与姿态变化的速度协调
解决姿态变化过快、位置变化过慢的问题
"""

import sys
import os

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

def test_speed_coordination():
    """测试速度协调配置"""
    print("🎯 位置与姿态速度协调测试")
    print("=" * 50)
    
    print("📊 当前速度配置分析:")
    print()
    
    # 全局速度设置
    global_speed = 100  # 运行模式全局速度
    
    # 修正前的配置
    print("❌ 修正前的配置:")
    old_g0_velocity = 100
    old_g1_velocity = 20
    old_g0_actual = global_speed * old_g0_velocity / 100
    old_g1_actual = global_speed * old_g1_velocity / 100
    print(f"   G0速度: {global_speed}% × {old_g0_velocity}% = {old_g0_actual}% (姿态变化太快)")
    print(f"   G1速度: {global_speed}% × {old_g1_velocity}% = {old_g1_actual}% (位置变化太慢)")
    print(f"   速度差异: {old_g0_actual - old_g1_actual}% (差异过大)")
    print()
    
    # 修正后的配置
    print("✅ 修正后的配置:")
    new_g0_velocity = 80
    new_g1_velocity = 60
    new_g0_actual = global_speed * new_g0_velocity / 100
    new_g1_actual = global_speed * new_g1_velocity / 100
    print(f"   G0速度: {global_speed}% × {new_g0_velocity}% = {new_g0_actual}% (姿态变化适中)")
    print(f"   G1速度: {global_speed}% × {new_g1_velocity}% = {new_g1_actual}% (位置变化合理)")
    print(f"   速度差异: {new_g0_actual - new_g1_actual}% (差异合理)")
    print()
    
    print("🔍 改进效果:")
    print(f"   G0速度降低: {old_g0_actual}% → {new_g0_actual}% (降低{old_g0_actual - new_g0_actual}%)")
    print(f"   G1速度提升: {old_g1_actual}% → {new_g1_actual}% (提升{new_g1_actual - old_g1_actual}%)")
    print(f"   速度协调性: 大幅改善")
    print()

def test_motion_coordination():
    """测试运动协调性"""
    print("🤖 运动协调性分析")
    print("=" * 30)
    
    print("🎯 理想的运动特性:")
    print("   G0 (快速定位): 快速但不过快，确保姿态平稳")
    print("   G1 (精确加工): 适中速度，确保加工质量")
    print("   协调性: 两种运动模式速度差异合理")
    print()
    
    print("⚖️ 速度平衡原则:")
    print("   1. G0应该比G1快，但不能快太多")
    print("   2. G1速度要足够保证加工质量")
    print("   3. 姿态变化不能比位置变化快太多")
    print("   4. 整体运动要流畅协调")
    print()
    
    print("📈 推荐速度范围:")
    print("   G0速度: 70-90% (快速但可控)")
    print("   G1速度: 50-70% (精确且高效)")
    print("   速度差异: 10-30% (保持层次感)")
    print()

def test_different_scenarios():
    """测试不同应用场景的速度配置"""
    print("🎨 不同应用场景速度配置")
    print("=" * 35)
    
    scenarios = [
        {
            "name": "精密加工",
            "g0": 70, "g1": 50,
            "description": "高精度要求，速度适中"
        },
        {
            "name": "快速原型",
            "g0": 80, "g1": 60,
            "description": "平衡速度与质量"
        },
        {
            "name": "粗加工",
            "g0": 90, "g1": 70,
            "description": "追求效率，质量要求较低"
        }
    ]
    
    print("场景类型    | G0速度 | G1速度 | 描述")
    print("-" * 45)
    
    for scenario in scenarios:
        print(f"{scenario['name']:8s} | {scenario['g0']:4d}%  | {scenario['g1']:4d}%  | {scenario['description']}")
    
    print()
    print("💡 当前配置 (快速原型): G0=80%, G1=60%")
    print("   适合大多数应用场景，平衡了速度与精度")
    print()

def test_acceleration_settings():
    """测试加速度设置"""
    print("⚡ 加速度配置分析")
    print("=" * 25)
    
    print("🔧 当前加速度设置:")
    print("   ACCEL_PERCENT = 20%")
    print()
    
    print("📊 加速度影响:")
    print("   • 过高: 运动突兀，机械冲击大")
    print("   • 过低: 运动缓慢，效率低")
    print("   • 适中: 平滑启停，保护机械")
    print()
    
    print("🎯 推荐加速度范围:")
    print("   精密加工: 15-25%")
    print("   一般应用: 20-30%")
    print("   快速应用: 25-40%")
    print()
    
    print("✅ 当前设置 (20%) 适合精密到一般应用")
    print()

def main():
    """主测试函数"""
    print("🔧 速度协调配置测试")
    print("=" * 60)
    print()
    
    test_speed_coordination()
    test_motion_coordination()
    test_different_scenarios()
    test_acceleration_settings()
    
    print("=" * 60)
    print("🎯 速度协调修复总结:")
    print()
    print("1. 🔧 问题识别:")
    print("   ❌ G0=100%, G1=20% → 姿态变化过快，位置变化过慢")
    print("   ❌ 速度差异80%，运动不协调")
    print()
    print("2. 🛠️ 解决方案:")
    print("   ✅ G0=80%, G1=60% → 速度更加协调")
    print("   ✅ 速度差异20%，保持合理层次")
    print()
    print("3. 📈 预期效果:")
    print("   • 姿态变化速度适中，不会过快")
    print("   • 位置变化速度提升，提高效率")
    print("   • 整体运动更加流畅协调")
    print("   • G0仍然比G1快，保持功能差异")
    print()
    print("4. ✅ 验证方法:")
    print("   运行改进后的improved_gcode_processor.py")
    print("   观察位置变化与姿态变化是否协调")
    print()

if __name__ == "__main__":
    main()
